<template>
  <BasicModal v-bind="$attrs" @register="registerModal" destroyOnClose :title="title" :width="896" @ok="handleSubmit">
      <BasicForm @register="registerForm" ref="formRef" name="ZyRiskFactorForm" />
      <!-- 子表单区域 -->
      <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
        <a-tab-pane tab="危害因素必检项目" key="zyRiskFactorItemgroup" :forceRender="true">
          <div class="mb-2">
            <a-button type="dashed" @click="addItemgroup" :disabled="formDisabled">新增</a-button>
          </div>
          <a-table
            :data-source="zyRiskFactorItemgroupTable.dataSource"
            :columns="itemgroupColumns"
            :pagination="false"
            :rowKey="row => row.id || row._key"
            :loading="zyRiskFactorItemgroupTable.loading"
            :scroll="{ y: 340, x: 1200 }"
            size="small"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.dataIndex === 'post'">
                <span>{{ record.post }}</span>
              </template>
              <template v-else-if="column.dataIndex === 'itemgroupId'">
                <span>{{ record.itemgroupId }}</span>
              </template>
              <template v-else-if="column.dataIndex === 'seq'">
                <span>{{ record.seq }}</span>
              </template>
              <template v-else-if="column.key === 'action'">
                <a-space>
                  <a-button type="link" @click="editItemgroup(record, index)" :disabled="formDisabled">编辑</a-button>
                  <a-button type="link" danger @click="removeItemgroup(record, index)" :disabled="formDisabled">删除</a-button>
                </a-space>
              </template>
            </template>
          </a-table>
          <BasicModal @register="registerRowModal" :title="rowModalTitle" :width="640" @ok="handleRowOk">
            <BasicForm @register="registerRowForm" />
          </BasicModal>
        </a-tab-pane>
      </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner, useModal} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
    import JSearchSelect from '/@/components/Form/src/jeecg/components/JSearchSelect.vue';
    import {formSchema} from '../ZyRiskFactor.data';
    import type { ZyRiskFactorItemgroupRow } from '../ZyRiskFactor.data';
    import {saveOrUpdate,queryZyRiskFactorItemgroup} from '../ZyRiskFactor.api';
    import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils'
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const refKeys = ref(['zyRiskFactorItemgroup', ]);
    const activeKey = ref('zyRiskFactorItemgroup');
    const zyRiskFactorItemgroup = ref();
    const tableRefs = {zyRiskFactorItemgroup, };
    const zyRiskFactorItemgroupTable = reactive({
          loading: false,
          dataSource: [] as ZyRiskFactorItemgroupRow[]
    })
    const itemgroupColumns = [
      { title: '岗位', dataIndex: 'post', width: 260 },
      { title: '项目组合', dataIndex: 'itemgroupId', width: 480 },
      { title: '排序号', dataIndex: 'seq', width: 160 },
      { title: '操作', key: 'action', width: 120, fixed: 'right' },
    ];
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 12}
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
             requestSubTableData(queryZyRiskFactorItemgroup, {id:data?.record?.id}, zyRiskFactorItemgroupTable)
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    // 方法：替代 useJvxeMethod
    const formRef = ref();
    function handleChangeTabs() {}
    async function requestSubTableData(api, params, table) {
      table.loading = true;
      try {
        const res = await api(params);
        table.dataSource = (res || []).map((row, idx) => ({
          id: row.id,
          post: row.post,
          itemgroupId: row.itemgroupId,
          seq: row.seq,
        }));
      } finally {
        table.loading = false;
      }
    }

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

    async function reset(){
      await resetFields();
      activeKey.value = 'zyRiskFactorItemgroup';
      zyRiskFactorItemgroupTable.dataSource = [];
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main,
           zyRiskFactorItemgroupList: zyRiskFactorItemgroupTable.dataSource.map((row: ZyRiskFactorItemgroupRow, idx) => ({
             id: row.id,
             post: row.post,
             itemgroupId: row.itemgroupId,
             seq: row.seq ?? idx + 1,
           })),
         }
       }
    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }

    // 行编辑弹窗
    const rowModalTitle = ref('编辑必检项目');
    const [registerRowModal, { openModal: openRowModal, closeModal: closeRowModal }] = useModal();
    const [registerRowForm, { setFieldsValue: setRowFieldsValue, resetFields: resetRowFields, validate: validateRowForm }] = useForm({
      labelWidth: 120,
      showActionButtonGroup: false,
      schemas: [
        { label: '岗位', field: 'post', component: 'JDictSelectTag', componentProps: { dictCode: 'job_status' } },
        { label: '项目组合', field: 'itemgroupId', component: 'JSearchSelect', componentProps: { dict: 'item_group where enable_flag=1 and del_flag=0,name,id' } },
        { label: '排序号', field: 'seq', component: 'InputNumber' },
      ],
    });
    let editingIndex = -1;
    function editItemgroup(record, index) {
      editingIndex = index;
      resetRowFields();
      setRowFieldsValue({ ...record });
      rowModalTitle.value = '编辑必检项目';
      openRowModal(true, {});
    }
    async function handleRowOk() {
      const values = await validateRowForm();
      if (editingIndex >= 0) {
        zyRiskFactorItemgroupTable.dataSource.splice(editingIndex, 1, { ...zyRiskFactorItemgroupTable.dataSource[editingIndex], ...values });
      } else {
        zyRiskFactorItemgroupTable.dataSource.push(values as any);
      }
      closeRowModal();
    }

    async function handleSubmit() {
      const allValues = await (formRef as any).value?.validate?.();
      if (!allValues) return;
      const payload = classifyIntoFormData({ formValue: allValues, tablesValue: [] });
      await requestAddOrEdit(payload);
    }

    function addItemgroup() {
      zyRiskFactorItemgroupTable.dataSource.push({} as ZyRiskFactorItemgroupRow);
    }
    function removeItemgroup(row, index) {
      zyRiskFactorItemgroupTable.dataSource.splice(index, 1);
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>