<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
  <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="危害因素必检项目" key="zyRiskFactorItemgroup" :forceRender="true">
        <div class="mb-2">
          <a-button type="dashed" @click="addItemgroup" :disabled="formDisabled">新增</a-button>
        </div>
        <a-table
          :data-source="zyRiskFactorItemgroupTable.dataSource"
          :columns="itemgroupColumns"
          :pagination="false"
          :rowKey="row => row.id || row._key"
          :loading="zyRiskFactorItemgroupTable.loading"
          :scroll="{ y: 340, x: 1200 }"
          size="small"
        >
          <template #bodyCell="{ column, record, index }">
            <template v-if="column.dataIndex === 'post'">
              <span>{{ renderDictText('job_status', record.post) }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'itemgroupId'">
              <span>{{ renderDictText('item_group where enable_flag=1 and del_flag=0,name,id', record.itemgroupId) }}</span>
            </template>
            <template v-else-if="column.dataIndex === 'seq'">
              <span>{{ record.seq }}</span>
            </template>
            <template v-else-if="column.key === 'action'">
              <a-space>
                <a-button type="link" @click="editItemgroup(record, index)" :disabled="formDisabled">编辑</a-button>
                <a-button type="link" danger @click="removeItemgroup(record, index)" :disabled="formDisabled">删除</a-button>
              </a-space>
            </template>
          </template>
        </a-table>
        <BasicModal @register="registerRowModal" :title="rowModalTitle" :width="640" @ok="handleRowOk">
          <BasicForm @register="registerRowForm" />
        </BasicModal>
      </a-tab-pane>
    </a-tabs>
    <div style="width: 100%;text-align: center;margin-top: 10px;" v-if="showFlowSubmitButton">
      <a-button preIcon="ant-design:check-outlined" style="width: 126px" type="primary" @click="handleSubmit">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
    import { defHttp } from '/@/utils/http/axios';
    import {ref, computed, reactive, onMounted, defineProps } from 'vue';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import { BasicModal, useModal } from '/@/components/Modal';
    import {formSchema} from '../ZyRiskFactor.data';
    import type { ZyRiskFactorItemgroupRow } from '../ZyRiskFactor.data';
    import {saveOrUpdate, zyRiskFactorItemgroupList} from '../ZyRiskFactor.api';
    import { getDictItemsByCode } from '/@/utils/dict/index';
    
    const activeKey = ref('zyRiskFactorItemgroup');
    const zyRiskFactorItemgroup = ref();
    const zyRiskFactorItemgroupTable = reactive({
          loading: false,
          dataSource: [] as ZyRiskFactorItemgroupRow[],
          show: false
    })
    const itemgroupColumns = [
      { title: '岗位', dataIndex: 'post', width: 260 },
      { title: '项目组合', dataIndex: 'itemgroupId', width: 480 },
      { title: '排序号', dataIndex: 'seq', width: 160 },
      { title: '操作', key: 'action', width: 120, fixed: 'right' },
    ];

    function renderDictText(dictCode: string, value: any) {
      const arr = getDictItemsByCode(dictCode) || [];
      const hit = arr.find((it) => String(it.value) === String(value));
      return hit ? hit.text : value;
    }

    const props = defineProps({
      formData: { type: Object, default: ()=>{} },
      formBpm: { type: Boolean, default: true }
    });
    const formDisabled = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return false;
        }
      }
      return true;
    });
    // 是否显示提交按钮
    const showFlowSubmitButton = computed(()=>{
      if(props.formBpm === true){
        if(props.formData.disabled === false){
          return true
        }
      }
      return false
    });
    
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
      labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 12}
    });

    onMounted(()=>{
      initFormData();
    });
    //渲染流程表单数据
    const queryByIdUrl = '/occu/zyRiskFactor/queryById';
    async function initFormData(){
      if(props.formBpm === true){
        await reset();
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //表单赋值
        await setFieldsValue({
          ...data
        });
        requestSubTableData(zyRiskFactorItemgroupList, {id: data.id}, zyRiskFactorItemgroupTable, ()=>{
          zyRiskFactorItemgroupTable.show = true;
        });
        // 隐藏底部时禁用整个表单
        setProps({ disabled: formDisabled.value })
      }
    }
    
    //方法配置
    // 替代 useJvxeMethod：最小化改造，保留原有调用点
    const formRef = ref();
    function handleChangeTabs() {}
    async function requestSubTableData(api, params, table, done) {
      table.loading = true;
      try {
        const res = await api(params);
        table.dataSource = (res?.result || []).map((row, idx) => ({
          id: row.id,
          post: row.post,
          itemgroupId: row.itemgroupId,
          seq: row.seq,
        }));
        done && done();
      } finally {
        table.loading = false;
      }
    }

    async function reset(){
      await resetFields();
      activeKey.value = 'zyRiskFactorItemgroup';
      zyRiskFactorItemgroupTable.dataSource = [];
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main,
           zyRiskFactorItemgroupList: zyRiskFactorItemgroupTable.dataSource.map((row: ZyRiskFactorItemgroupRow, idx) => ({
             id: row.id,
             post: row.post,
             itemgroupId: row.itemgroupId,
             seq: row.seq ?? idx + 1,
           })),
         }
       }
    //表单提交事件
    async function requestAddOrEdit(values) {
      //提交表单
      await saveOrUpdate(values, true);
    }

    function addItemgroup() {
      editingIndex = -1;
      resetRowFields();
      rowModalTitle.value = '新增必检项目';
      openRowModal(true, {});
    }
    function removeItemgroup(_row, index) {
      zyRiskFactorItemgroupTable.dataSource.splice(index, 1);
    }

    // 行编辑弹窗
    const rowModalTitle = ref('编辑必检项目');
    const [registerRowModal, { openModal: openRowModal, closeModal: closeRowModal }] = useModal();
    const [registerRowForm, { setFieldsValue: setRowFieldsValue, resetFields: resetRowFields, validate: validateRowForm }] = useForm({
      labelWidth: 120,
      showActionButtonGroup: false,
      schemas: [
        { label: '岗位', field: 'post', component: 'JDictSelectTag', componentProps: { dictCode: 'job_status' } },
        { label: '项目组合', field: 'itemgroupId', component: 'JSearchSelect', componentProps: { dict: 'item_group where enable_flag=1 and del_flag=0,name,id' } },
        { label: '排序号', field: 'seq', component: 'InputNumber' },
      ],
    });
    let editingIndex = -1;
    function editItemgroup(record, index) {
      editingIndex = index;
      resetRowFields();
      setRowFieldsValue({ ...record });
      rowModalTitle.value = '编辑必检项目';
      openRowModal(true, {});
    }
    async function handleRowOk() {
      const values = await validateRowForm();
      if (editingIndex >= 0) {
        zyRiskFactorItemgroupTable.dataSource.splice(editingIndex, 1, { ...zyRiskFactorItemgroupTable.dataSource[editingIndex], ...values });
      } else {
        zyRiskFactorItemgroupTable.dataSource.push(values as ZyRiskFactorItemgroupRow);
      }
      closeRowModal();
    }

    // 主表提交
    async function handleSubmit() {
      const formValue = await validate();
      const payload = classifyIntoFormData({ formValue });
      await requestAddOrEdit(payload);
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>