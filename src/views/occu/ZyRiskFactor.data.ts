import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
// JVxe 已移除，改用普通表格与内联编辑/弹窗编辑
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '排序',
    align:"center",
    dataIndex: 'sort'
   },
   {
    title: '名称',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '代码',
    align:"center",
    dataIndex: 'code'
   },
   {
    title: '类别',
    align:"center",
    dataIndex: 'typeId_dictText'
   },
   {
    title: '启用',
    align:"center",
    dataIndex: 'valid',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])
     },
   },
   {
    title: '系统内置',
    align:"center",
    dataIndex: 'sysFlag',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'1'},{text:'否',value:'0'}])
     },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "名称",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "代码",
      field: 'code',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "类别",
      field: 'typeId',
      component: 'JSearchSelect',
      componentProps:{
         dict:"zy_risk_factor_type where enable_flag=1,name,id"
      },
      //colProps: {span: 6},
 	},
	{
      label: "启用",
      field: 'valid',
      component: 'JSwitch',
      componentProps:{
           query:true,
           options:[1,0]
       },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '排序',
    field: 'sort',
    component: 'InputNumber',
  },
  {
    label: '名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入名称!'},
          ];
     },
  },
  {
    label: '代码',
    field: 'code',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入代码!'},
          ];
     },
  },
  {
    label: '类别',
    field: 'typeId',
    component: 'JSearchSelect',
    componentProps:{
       dict:"zy_risk_factor_type where enable_flag=1,name,id"
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入类别!'},
          ];
     },
  },
  {
    label: '启用',
    field: 'valid',
     component: 'JSwitch',
     componentProps:{
         options:[1,0]
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入启用!'},
          ];
     },
  },
  {
    label: '系统内置',
    field: 'sysFlag',
     component: 'JSwitch',
     componentProps:{
         options:[1,0]
     },
  },
  {
    label: '防护措施',
    field: 'protectAdvice',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
//子表列表数据
export const zyRiskFactorItemgroupColumns: BasicColumn[] = [
   {
    title: '岗位',
    align:"center",
    dataIndex: 'post_dictText'
   },
   {
    title: '项目组合',
    align:"center",
    dataIndex: 'itemgroupId_dictText'
   },
   {
    title: '排序号',
    align:"center",
    dataIndex: 'seq'
   },
];
//子表表格配置
export interface ZyRiskFactorItemgroupRow {
  id?: string;
  post?: string;
  itemgroupId?: string;
  seq?: number;
}

// 高级查询数据
export const superQuerySchema = {
  sort: {title: '排序',order: 0,view: 'number', type: 'number',},
  name: {title: '名称',order: 1,view: 'text', type: 'string',},
  code: {title: '代码',order: 2,view: 'text', type: 'string',},
  typeId: {title: '类别',order: 3,view: 'sel_search', type: 'string',dictTable: "zy_risk_factor_type where enable_flag=1", dictCode: 'id', dictText: 'name',},
  valid: {title: '启用',order: 4,view: 'number', type: 'number',},
  sysFlag: {title: '系统内置',order: 5,view: 'switch', type: 'string',},
  //子表高级查询
  zyRiskFactorItemgroup: {
    title: '危害因素必检项目',
    view: 'table',
    fields: {
        post: {title: '岗位',order: 0,view: 'list', type: 'string',dictCode: 'job_status',},
        itemgroupId: {title: '项目组合',order: 1,view: 'sel_search', type: 'string',dictTable: "item_group where enable_flag=1 and del_flag=0", dictCode: 'id', dictText: 'name',},
        seq: {title: '排序号',order: 2,view: 'number', type: 'number',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}