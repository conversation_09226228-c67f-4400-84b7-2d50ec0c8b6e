import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '问卷名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '问卷编码',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '适用模块',
    align: 'center',
    dataIndex: 'module_dictText',
  },
];

//子表列表数据
export const questionnaireContentColumns: BasicColumn[] = [
  {
    title: '题号',
    align: 'center',
    dataIndex: 'sort',
  },
  {
    title: '题目',
    align: 'center',
    dataIndex: 'content',
  },
  {
    title: '类型',
    align: 'center',
    dataIndex: 'type_dictText',
  },
  {
    title: '是否必答',
    align: 'center',
    dataIndex: 'requiredFlag',
    customRender: ({ record }) => {
      return record.requiredFlag == '1' ? '是' : '否';
    },
  },
];
// 选项与计算结果的行类型定义，供表单组件使用
export interface TopicOptionRow {
  id?: string;
  optionNumber?: string | number;
  content?: string;
  score?: string | number;
  sortNumber?: number;
}

export interface CalResultRow {
  id?: string;
  groovyScript?: string;
  result?: string;
  score?: string; // 建议
  sortNumber?: number;
}
//子表列表数据
export const questionnaireCalculationColumns: BasicColumn[] = [
  {
    title: '维度',
    align: 'center',
    dataIndex: 'dimension',
  },
  {
    title: '显示选择',
    align: 'center',
    dataIndex: 'displaySelection_dictText',
  },
  {
    title: '显示结果(得分/结果）',
    align: 'center',
    dataIndex: 'displayResult_dictText',
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'sortNumber',
  },
  {
    title: '计算脚本',
    align: 'center',
    dataIndex: 'calScript',
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '问卷名称', order: 0, view: 'text', type: 'string' },
  module: { title: '适用模块', order: 1, view: 'list', type: 'string', dictCode: 'quest_suit_module' },
};
