<template>
  <div style="padding: 10px">
    <a-spin :spinning="confirmLoading">
      <JFormContainer :disabled="disabled">
        <template #detail>
          <a-form class="antd-modal-form" v-bind="formItemLayout" ref="formRef" name="QuestionnaireCalculationForm">
            <a-row>
              <a-col :span="12">
                <a-form-item label="维度" v-bind="validateInfos.dimension" id="QuestionnaireCalculation-dimension" name="dimension">
                  <a-input v-model:value="formData.dimension" placeholder="请输入维度" allow-clear />
                </a-form-item>
              </a-col>
              <!--            <a-col :span="12">
              <a-form-item
                label="显示选择"
                v-bind="validateInfos.displaySelection"
                id="QuestionnaireCalculation-displaySelection"
                name="displaySelection"
              >
                <j-dict-select-tag v-model:value="formData.displaySelection" dictCode="" placeholder="请选择显示选择" allow-clear />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item
                label="显示结果(得分/结果）"
                v-bind="validateInfos.displayResult"
                id="QuestionnaireCalculation-displayResult"
                name="displayResult"
              >
                <j-dict-select-tag v-model:value="formData.displayResult" dictCode="" placeholder="请选择显示结果(得分/结果）" allow-clear />
              </a-form-item>
            </a-col>-->
              <a-col :span="12">
                <a-form-item label="排序号" v-bind="validateInfos.sortNumber" id="QuestionnaireCalculation-sortNumber" name="sortNumber">
                  <a-input-number v-model:value="formData.sortNumber" placeholder="请输入排序号" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item
                  :label-col="{ span: 2 }"
                  :wrapper-col="{ span: 22 }"
                  label="计算脚本"
                  v-bind="validateInfos.calScript"
                  id="QuestionnaireCalculation-calScript"
                  name="calScript"
                >
                  <a-textarea v-model:value="formData.calScript" :rows="4" placeholder="请输入计算脚本" />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </template> </JFormContainer
      >+

      <a-divider orientation="left">计算结果</a-divider>
      <a-alert
        show-icon
        type="info"
        message="计算结果中计算脚本示例：def isMatch(calResult){return false;} 入参calResult是维护计算脚本的返回值，返回true表示匹配该结果，返回false表示不匹配。"
      />
      <div class="mb-2">
        <a-button type="dashed" @click="addCalResult">新增结果</a-button>
      </div>
      <a-table
        :data-source="calResultTable.dataSource"
        :columns="calResultColumns"
        :pagination="false"
        :rowKey="row => row.id || row._key"
        :loading="calResultTable.loading"
        :scroll="{ y: 340, x: 1000 }"
        size="small"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'groovyScript'">
            <a-textarea v-model:value="record.groovyScript" :rows="2" placeholder="匹配测试脚本" />
          </template>
          <template v-else-if="column.dataIndex === 'result'">
            <a-textarea v-model:value="record.result" :rows="2" placeholder="结果" />
          </template>
          <template v-else-if="column.dataIndex === 'score'">
            <a-textarea v-model:value="record.score" :rows="2" placeholder="建议" />
          </template>
          <template v-else-if="column.key === 'action'">
            <a-button type="link" danger @click="removeCalResult(record, index)">删除</a-button>
          </template>
        </template>
      </a-table>
    </a-spin>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, onMounted, inject, defineProps, unref } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { getCalResultByCalId, getOptonsByContentId, questionnaireCalculationSaveOrUpdate } from '../QuestionnaireDefinition.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import type { CalResultRow } from '@/views/quest/QuestionnaireDefinition.data';
  import { deleteCalResult } from '@/views/quest/QuestionCalculationResult.api';

  const calResultTable = reactive({
    loading: false,
    dataSource: [] as CalResultRow[],
    show: false,
  });
  const calResultColumns = [
    { title: '匹配测试脚本', dataIndex: 'groovyScript', width: 420 },
    { title: '结果', dataIndex: 'result', width: 300 },
    { title: '建议', dataIndex: 'score', width: 300 },
    { title: '操作', key: 'action', width: 120, fixed: 'right' },
  ];

  //接收主表id
  const mainId = inject('mainId');
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    dimension: '',
    displaySelection: '',
    displayResult: '',
    sortNumber: undefined,
    calScript: '',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = {
    dimension: [{ required: true, message: '请输入维度!' }],
    sortNumber: [{ required: true, message: '请输入排序号!' }],
  };
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });
  const props = defineProps({
    disabled: { type: Boolean, default: false },
  });
  const formItemLayout = {
    labelCol: { xs: { span: 24 }, sm: { span: 5 } },
    wrapperCol: { xs: { span: 24 }, sm: { span: 16 } },
  };

  function removeCalResult(row, index) {
    if (row.id) {
      deleteCalResult({ id: row.id }).then(() => {
        calResultTable.dataSource.splice(index, 1);
      });
    } else {
      calResultTable.dataSource.splice(index, 1);
    }
  }

  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if (record.hasOwnProperty(key)) {
          tmpData[key] = record[key];
        }
      });
      //赋值
      Object.assign(formData, tmpData);
      if (record.id) {
        initCalResult(record.id);
      }
    });
  }

  function initCalResult(calId) {
    getCalResultByCalId({ calId: calId }).then((res) => {
      calResultTable.dataSource = (res.result || []).map((row, idx) => ({
        id: row.id,
        groovyScript: row.groovyScript,
        result: row.result,
        score: row.score,
      }));
    });
  }

  function addCalResult() {
    calResultTable.dataSource.push({
      groovyScript: 'def isMatch(calResult){return false;}',
      result: '',
      score: '',
    } as CalResultRow);
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    try {
      // 触发表单验证
      await validate();
    } catch ({ errorFields }) {
      if (errorFields) {
        const firstField = errorFields[0];
        if (firstField) {
          formRef.value.scrollToField(firstField.name, { behavior: 'smooth', block: 'center' });
        }
      }
      return Promise.reject(errorFields);
    }
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }

    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    if (unref(mainId)) {
      model['questionnaireId'] = unref(mainId);
    }
    model.resultList = calResultTable.dataSource.map((row: CalResultRow, idx) => ({
      id: row.id,
      groovyScript: row.groovyScript ?? '',
      result: row.result ?? '',
      score: row.score ?? '',
      sortNumber: idx + 1,
    }));
    await questionnaireCalculationSaveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
