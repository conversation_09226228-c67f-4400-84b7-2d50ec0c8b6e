<template>
  <BasicModal ref="modalRef" destroyOnClose wrapClassName="j-cgform-tab-modal" v-bind="$attrs" @register="registerModal" :width="800" @ok="handleSubmit">
    <!-- 标题区域 -->
     <template #title>
        <div class="titleArea">
          <div class="title">{{ title }}</div>
          <div class="right">
            <a-radio-group v-model:value="activeKey">
              <template v-for="(item, index) in tabNav" :key="index">
                <a-radio-button :value="item.tableName">{{ item.tableTxt }}</a-radio-button>
              </template>
            </a-radio-group>
          </div>
        </div>
     </template>
     <!--表单区域 -->
     <div class="contentArea">
       <!--主表区域 -->
        <BasicForm @register="registerForm" ref="formRef" v-show="activeKey == refKeys[0]" name="ReportSettingGroupForm"/>
       <!--子表区域 -->
         <div v-show="activeKey == 'reportSettingDepart'">
           <div style="margin-bottom: 16px;">
             <a-button type="primary" @click="addDepartRow">添加科室</a-button>
             <a-button style="margin-left: 8px;" @click="deleteDepartRows" :disabled="reportSettingDepartTable.selectedRowKeys.length === 0">删除选中</a-button>
           </div>
           <a-table
             ref="reportSettingDepart"
             :loading="reportSettingDepartTable.loading"
             :columns="reportSettingDepartColumns"
             :data-source="reportSettingDepartTable.dataSource"
             :scroll="{ y: 340 }"
             :pagination="false"
             row-key="id"
             size="small"
             :row-selection="{ selectedRowKeys: reportSettingDepartTable.selectedRowKeys, onChange: onDepartSelectionChange }"
           >
             <template #bodyCell="{ column, text, record, index }">
               <template v-if="column.dataIndex === 'departmentId'">
                 <a-select v-model:value="record.departmentId" placeholder="请选择科室" style="width: 100%">
                   <!-- 这里需要根据实际情况添加科室选项 -->
                   <a-select-option value="1">科室1</a-select-option>
                   <a-select-option value="2">科室2</a-select-option>
                 </a-select>
               </template>
               <template v-else-if="column.dataIndex === 'seq'">
                 <a-input-number v-model:value="record.seq" placeholder="请输入排序" style="width: 100%" />
               </template>
             </template>
           </a-table>
         </div>

         <div v-show="activeKey == 'reportSettingItemgroup'">
           <div style="margin-bottom: 16px;">
             <a-button type="primary" @click="addItemgroupRow">添加大项</a-button>
             <a-button style="margin-left: 8px;" @click="deleteItemgroupRows" :disabled="reportSettingItemgroupTable.selectedRowKeys.length === 0">删除选中</a-button>
           </div>
           <a-table
             ref="reportSettingItemgroup"
             :loading="reportSettingItemgroupTable.loading"
             :columns="reportSettingItemgroupColumns"
             :data-source="reportSettingItemgroupTable.dataSource"
             :scroll="{ y: 340 }"
             :pagination="false"
             row-key="id"
             size="small"
             :row-selection="{ selectedRowKeys: reportSettingItemgroupTable.selectedRowKeys, onChange: onItemgroupSelectionChange }"
           >
             <template #bodyCell="{ column, text, record, index }">
               <template v-if="column.dataIndex === 'itemgroupId'">
                 <a-select v-model:value="record.itemgroupId" placeholder="请选择大项" style="width: 100%">
                   <!-- 这里需要根据实际情况添加大项选项 -->
                   <a-select-option value="1">大项1</a-select-option>
                   <a-select-option value="2">大项2</a-select-option>
                 </a-select>
               </template>
               <template v-else-if="column.dataIndex === 'seq'">
                 <a-input-number v-model:value="record.seq" placeholder="请输入排序号" style="width: 100%" />
               </template>
             </template>
           </a-table>
         </div>
     </div>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/@/components/Modal';
    import {BasicForm, useForm} from '/@/components/Form/index';
    import {formSchema,reportSettingDepartColumns,reportSettingItemgroupColumns} from '../ReportSettingGroup.data';
    import {saveOrUpdate,reportSettingDepartList,reportSettingItemgroupList} from '../ReportSettingGroup.api';
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const modalRef = ref();
    const refKeys = ref(['reportSettingGroup','reportSettingDepart', 'reportSettingItemgroup', ]);
    const tabNav = ref<any>([
      { tableName: 'reportSettingGroup', tableTxt: '报告分组设置' },
       { tableName: 'reportSettingDepart', tableTxt: '报告分组设置-关联科室' },
       { tableName: 'reportSettingItemgroup', tableTxt: '报告分组设置-关联大项' },
    ]);
    const activeKey = ref('reportSettingGroup');
    const reportSettingDepart = ref();
    const reportSettingItemgroup = ref();
    const reportSettingDepartTable = reactive({
          loading: false,
          dataSource: [],
          selectedRowKeys: []
    })
    const reportSettingItemgroupTable = reactive({
          loading: false,
          dataSource: [],
          selectedRowKeys: []
    })
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {span: 24}
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
             requestSubTableData(reportSettingDepartList, {id:data?.record?.id}, reportSettingDepartTable)
             requestSubTableData(reportSettingItemgroupList, {id:data?.record?.id}, reportSettingItemgroupTable)
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const formRef = ref();

    // 请求子表数据
    async function requestSubTableData(apiFunc, params, tab) {
      tab.loading = true;
      try {
        const result = await apiFunc(params);
        if (result && Array.isArray(result)) {
          tab.dataSource = result;
        } else {
          tab.dataSource = [];
        }
      } catch (error) {
        console.error('加载子表数据失败:', error);
        tab.dataSource = [];
      } finally {
        tab.loading = false;
      }
    }
    // 弹窗tabs滚动区域的高度
    const tabsStyle = computed(() => {
      let height: Nullable<string> = null
      let minHeight = '100px'
      let maxHeight: Nullable<string> = '500px'
      // 弹窗wrapper
      let modalWrapperRef = modalRef.value?.modalWrapperRef
      if (modalWrapperRef) {
        if (modalWrapperRef.fullScreen) {
          height = 'calc(' + modalWrapperRef.spinStyle.height + ' - 50px)';
          maxHeight = null
        }
      }
      let overflow = 'auto';
      return {height, minHeight, maxHeight, overflow};
    })
    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));
    //重置
    async function reset(){
      await resetFields();
      activeKey.value = 'reportSettingGroup';
      reportSettingDepartTable.dataSource = [];
      reportSettingDepartTable.selectedRowKeys = [];
      reportSettingItemgroupTable.dataSource = [];
      reportSettingItemgroupTable.selectedRowKeys = [];
    }
    // 表格操作方法
    function addDepartRow() {
      const newRow = {
        id: Date.now().toString(),
        departmentId: '',
        seq: reportSettingDepartTable.dataSource.length + 1
      };
      reportSettingDepartTable.dataSource.push(newRow);
    }

    function deleteDepartRows() {
      reportSettingDepartTable.dataSource = reportSettingDepartTable.dataSource.filter(
        item => !reportSettingDepartTable.selectedRowKeys.includes(item.id)
      );
      reportSettingDepartTable.selectedRowKeys = [];
    }

    function addItemgroupRow() {
      const newRow = {
        id: Date.now().toString(),
        itemgroupId: '',
        seq: reportSettingItemgroupTable.dataSource.length + 1
      };
      reportSettingItemgroupTable.dataSource.push(newRow);
    }

    function deleteItemgroupRows() {
      reportSettingItemgroupTable.dataSource = reportSettingItemgroupTable.dataSource.filter(
        item => !reportSettingItemgroupTable.selectedRowKeys.includes(item.id)
      );
      reportSettingItemgroupTable.selectedRowKeys = [];
    }

    function onDepartSelectionChange(selectedRowKeys) {
      reportSettingDepartTable.selectedRowKeys = selectedRowKeys;
    }

    function onItemgroupSelectionChange(selectedRowKeys) {
      reportSettingItemgroupTable.selectedRowKeys = selectedRowKeys;
    }

    function classifyIntoFormData() {
         let main = formRef.value.getFieldsValue();
         return {
           ...main,
           reportSettingDepartList: reportSettingDepartTable.dataSource,
           reportSettingItemgroupList: reportSettingItemgroupTable.dataSource,
         }
       }
    // 表单提交事件
    async function handleSubmit() {
        try {
            // 验证主表单
            await validate();

            // 获取表单数据
            const formData = classifyIntoFormData();

            setModalProps({confirmLoading: true});
            //提交表单
            await saveOrUpdate(formData, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } catch (error) {
            console.error('表单提交失败:', error);
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>
  /** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }

  .titleArea {
    display: flex;
    align-content: center;
    padding-right: 70px;
    .title {
      margin-right: 16px;
      line-height: 32px;
    }
    .right {
      overflow-x: auto;
      overflow-y: hidden;
      flex: 1;
      white-space: nowrap;
      .ant-radio-group {
       font-weight: normal;
      }
    }
  }

  html[data-theme='light'] {
      .right {
        .ant-radio-group {
          :deep(.ant-radio-button-wrapper:not(.ant-radio-button-wrapper-checked)) {
            color: #555;
          }
        }
      }
    }
</style>

<style lang="less">
// Online表单Tab风格专属样式
.j-cgform-tab-modal {
   .contentArea {
      padding: 20px 1.5% 0;
    }

     //.ant-modal-header {
     //  padding-top: 8px;
     //  padding-bottom: 8px;
     //  border-bottom: none !important;
     //}

  .ant-modal .ant-modal-body > .scrollbar,
  .ant-tabs-nav .ant-tabs-tab {
    padding-top: 0;
  }

  .ant-tabs-top-bar {
    width: calc(100% - 55px);
    position: relative;
    left: -14px;
  }

  .ant-tabs .ant-tabs-top-content > .ant-tabs-tabpane {
    overflow: hidden auto;
  }
}
</style>
