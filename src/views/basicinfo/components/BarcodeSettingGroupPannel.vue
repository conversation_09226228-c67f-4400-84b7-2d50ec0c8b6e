<template>
  <div style="height: 75vh">
    <a-row :gutter="8">
      <a-col :span="12">
        <a-card size="small" title="待选组合" style="padding: 5px; height: 75vh">
          <a-row :gutter="8">
            <a-col :span="8">
              <div style="max-height: 65vh; overflow-y: scroll">
                <depart-tree @select="searchGroupByDepartment" />
              </div>
            </a-col>
            <a-col :span="16">
              <a-form layout="inline" style="margin-bottom: 10px">
                <a-form-item labelAlign="left">
                  <a-input-search allow-clear @change="searchGroupByKeyword" size="middle" placeholder="名称或助记码查询" />
                </a-form-item>
                <a-flex justify="right">
                  <a-button type="primary" @click="handleAdd" :disabled="groupTableState.selectedRows.length === 0">添加</a-button>
                </a-flex>
              </a-form>
              <a-table
                :loading="groupLoading"
                :bordered="false"
                :scroll="{ y: '60vh' }"
                :pagination="false"
                row-key="id"
                :columns="groupColumns"
                :data-source="groupDatasource"
                :custom-row="customRow"
                size="small"
                :row-selection="{ selectedRowKeys: groupTableState.selectedRowKeys, onChange: onGroupTableSelectChange }"
              >
                <template #bodyCell="{ text, record, index, column }">
                  <template v-if="column.dataIndex === 'operation'">
                    <a @click.stop="showItem(record)">项目</a>
                  </template>
                </template>
              </a-table>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card size="small" title="已选组合" style="padding: 5px; height: 75vh">
          <BarcodeSettingGroupList ref="barcodeSettingGroupList" />
        </a-card>
      </a-col>
    </a-row>

    <group-item-modal ref="groupItemModal" />
  </div>
</template>
<script lang="ts" setup>
  import { computed, inject, onMounted, reactive, ref } from 'vue';
  import type { Group, Key } from '#/types';
  import { message, TableColumnType } from 'ant-design-vue';
  import DepartTree from '@/views/basicinfo/components/DepartTree.vue';
  import GroupItemModal from '@/views/basicinfo/components/GroupItemModal.vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getAllGroup } from '@/views/basicinfo/ItemGroup.api';
  import BarcodeSettingGroupList from '@/views/basicinfo/BarcodeSettingGroupList.vue';
  import { saveGroup } from '@/views/basicinfo/BarcodeSettingGroup.api';

  const { createErrorModal } = useMessage();

  const groupItemModal = ref();
  const barcodeSettingGroupList = ref();
  const barcodeSettingMainId = inject('barcodeSettingMainId') || ref('');

  /**左侧项目组合相关操作*/
  let groupListInited = ref(false);
  const selectedDepartId = ref<string | null>(null);
  const groupList = ref<Group[]>([]);
  const groupLoading = ref<boolean>(false);
  const groupDatasource = ref<Group[]>([]);
  const groupTableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: Group[];
    loading: boolean;
    filteredDepart: string | null;
  }>({
    selectedRowKeys: [], // Check here to configure the default column
    selectedRows: [],
    loading: false,
    filteredDepart: null,
  });

  const customRow = (record) => {
    return {
      onClick: () => {
        if (groupTableState.selectedRowKeys.includes(record.id)) {
          groupTableState.selectedRowKeys = groupTableState.selectedRowKeys.filter((key) => key !== record.id);
          groupTableState.selectedRows = groupTableState.selectedRows.filter((row) => row.id !== record.id);
        } else {
          groupTableState.selectedRowKeys.push(record.id);
          groupTableState.selectedRows.push(record);
        }
      },
    };
  };

  const groupColumns = computed<TableColumnType[]>(() => {
    return [
      {
        title: '组合名称',
        dataIndex: 'name',
        width: '120',
      },
      {
        title: '价格',
        dataIndex: 'price',
        width: '60',
      },
      {
        title: '科室',
        dataIndex: 'departmentName',
        width: '100',
        ellipsis: true,
        customFilterDropdown: true,
        onFilter: (value, record) => {
          return record.departmentName == value;
        },
        onFilterDropdownOpenChange: (visible) => {
          if (visible) {
            //openHandle();
          }
        },
      },
      {
        title: '查看',
        dataIndex: 'operation',
        width: '60',
      },
    ];
  });

  function showItem(record) {
    groupItemModal.value.showDetail(record.id);
  }
  const searchGroupByDepartment = (depart) => {
    if (depart.isLeaf) {
      selectedDepartId.value = depart.id;
      groupDatasource.value = groupList.value.filter((item) => {
        return item.departmentId == depart.id;
      });
    } else {
      selectedDepartId.value = '';
      groupDatasource.value = groupList.value;
    }
  };
  const searchGroupByKeyword = (e) => {
    let keyword = e.target.value;
    if (keyword) {
      groupDatasource.value = groupList.value.filter((item) => {
        let departMatched = true;
        if (selectedDepartId.value) {
          departMatched = item.departmentId == selectedDepartId.value;
        }

        let wordMatched = item.name.includes(keyword) || item.helpChar?.toLowerCase().includes(keyword?.toLowerCase());
        if (wordMatched && departMatched) {
          return true;
        }
        return false;
      });
    } else {
      if (selectedDepartId.value) {
        groupDatasource.value = groupList.value.filter((item) => {
          return item.departmentId == selectedDepartId.value;
        });
      } else {
        groupDatasource.value = groupList.value;
      }
    }
  };

  const onGroupTableSelectChange = (selectedRowKeys: Key[], selectedRows: Group[]) => {
    groupTableState.selectedRowKeys = selectedRowKeys;
    groupTableState.selectedRows = selectedRows;
  };

  const fetchGroup = () => {
    if (!groupListInited.value) {
      groupLoading.value = true;
      getAllGroup({})
        .then((res) => {
          groupList.value = res;
          groupDatasource.value = res;
          groupListInited.value = true;
        })
        .finally(() => {
          groupLoading.value = false;
        });
    }
  };

  // 添加选中的行到另一个表格
  function handleAdd() {
    //console.log('saveSelectedGroup================', barcodeSettingGroupList.value.getDataSource);
    let barcodeSettingDataSource = barcodeSettingGroupList.value.getDataSource();

    //需要排除掉已经存在的
    let addList = groupTableState.selectedRows
      .filter((item) => !barcodeSettingDataSource.find((record) => record.groupId === item.id))
      .map((item) => {
        return {
          settingId: barcodeSettingMainId.value,
          groupId: item.id,
          groupName: item.name,
          hisCode: item.hisCode,
          hisName: item.hisName,
          validFlag: '1',
        };
      });
    //console.log('saveSelectedGroup2================', addList);

    if (addList.length > 0) {
      saveGroup(addList).then((res) => {
        if (res.success) {
          message.success('添加成功！');
          barcodeSettingGroupList.value.reload();
        } else {
          createErrorModal({ title: '添加失败', content: res.message });
        }
      });
    } else {
      message.info('所选组合已存在！');
    }
  }

  onMounted(() => {
    fetchGroup();
  });

  defineExpose({});
</script>
