import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '分组名称',
    align: 'center',
    dataIndex: 'name',
  },
  {
    title: '分组代码',
    align: 'center',
    dataIndex: 'code',
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'seq',
    sorter: true,
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '分组名称',
    field: 'name',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入分组名称!' }];
    },
  },
  {
    label: '分组代码',
    field: 'code',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入分组代码!' }, { ...rules.duplicateCheckRule('report_setting_group', 'code', model, schema)[0] }];
    },
  },
  {
    label: '排序号',
    field: 'seq',
    component: 'InputNumber',
    dynamicRules: ({ model, schema }) => {
      return [{ required: true, message: '请输入排序号!' }];
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
//子表表格配置
export const reportSettingDepartColumns: BasicColumn[] = [
  {
    title: '科室',
    align: 'center',
    dataIndex: 'departmentId',
    width: 200,
  },
  {
    title: '排序',
    align: 'center',
    dataIndex: 'seq',
    width: 200,
  },
];
export const reportSettingItemgroupColumns: BasicColumn[] = [
  {
    title: '大项',
    align: 'center',
    dataIndex: 'itemgroupId',
    width: 200,
  },
  {
    title: '排序号',
    align: 'center',
    dataIndex: 'seq',
    width: 200,
  },
];

// 高级查询数据
export const superQuerySchema = {
  name: { title: '分组名称', order: 0, view: 'text', type: 'string' },
  code: { title: '分组代码', order: 1, view: 'text', type: 'string' },
  seq: { title: '排序号', order: 2, view: 'number', type: 'number' },
  //子表高级查询
  reportSettingDepart: {
    title: '报告分组设置-关联科室',
    view: 'table',
    fields: {
      departmentId: { title: '科室', order: 0, view: 'sel_depart', type: 'string' },
      seq: { title: '排序', order: 1, view: 'number', type: 'number' },
    },
  },
  reportSettingItemgroup: {
    title: '报告分组设置-关联大项',
    view: 'table',
    fields: {
      itemgroupId: {
        title: '大项',
        order: 0,
        view: 'sel_search',
        type: 'string',
        dictTable: 'item_group where del_flag=0 and enable_flag=1',
        dictCode: 'id',
        dictText: 'name',
      },
      seq: { title: '排序号', order: 1, view: 'number', type: 'number' },
    },
  },
};
