<template>
  <a-spin :spinning="confirmLoading">
    <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
      <a-row>
        <a-col :span="24">
          <a-form-item label="危害因素" v-bind="validateInfos.riskFactorId">
            <!--            <a-typography-text strong v-if="formData.riskFactor">{{ formData.riskFactor }}</a-typography-text>-->
            <j-async-search-select
              v-model:value="formData.riskCode"
              @select="setName"
              dict="zy_risk_factor,name,code"
              :disabled="disabled"
              placeholder="请选择危害因素"
            />

            <!--            <j-dict-select-tag
              v-else
              v-model:value="formData.riskFactorId"
              dictCode="zy_risk_factor,name,id"
              placeholder="请选择危害因素"
              :disabled="disabled"
            />-->
          </a-form-item>
        </a-col>
        <!--        <a-col :span="24">
          <a-form-item label="工种" v-bind="validateInfos.workType">
            <span>{{ customerReg4Summary.workType_dictText }}</span>
          </a-form-item>
        </a-col>-->
        <a-col :span="24">
          <a-form-item label="主要危害因素" v-bind="validateInfos.mainFlag">
            <a-radio-group v-model:value="formData.mainFlag" :disabled="disabled">
              <a-radio value="1">是</a-radio>
              <a-radio value="0">否</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="职业检结论" v-bind="validateInfos.conclusion">
            <j-dict-select-tag
              v-model:value="formData.conclusion"
              dict-code="zy_conclusion_dict,dict_text,code"
              placeholder="请输选择职业检结论"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="结论依据" v-bind="validateInfos.according">
            <j-dict-select-tag
              v-model:value="formData.according"
              dictCode="zy_conclusion_according,content,content"
              placeholder="请选择依据"
              :disabled="disabled"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="处理意见" v-bind="validateInfos.advice">
            <a-popover trigger="hover" placement="bottomLeft">
              <template #title>
                <a-row>
                  <a-col :span="20">
                    <a-typography-text strong>字典</a-typography-text>
                  </a-col>
                  <a-col :span="4">
                    <a-button size="small" @click="handelAddItemDict">添加字典</a-button>
                  </a-col>
                </a-row>
              </template>

              <template #content>
                <a-input-search placeholder="请输入关键字检索" @change="filterDict" enter-button />
                <a-table
                  :columns="dictColumns"
                  :data-source="filtedDictList"
                  size="small"
                  :show-header="false"
                  :pagination="false"
                  :scroll="{ y: 300, x: 400 }"
                  :custom-row="click2UseItemDict"
                />
              </template>
              <a-textarea :rows="2" v-model:value="formData.advice" placeholder="请输入处理意见" :disabled="disabled" />
            </a-popover>
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="职业病" v-bind="validateInfos.zyDisease">
            <j-select-multiple
              type="list_multi"
              v-model:value="formData.zyDisease"
              dictCode="zy_disease_dict,dict_text,code"
              placeholder="请选择职业病"
              :disabled="disabled"
              :triggerChange="false"
            />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="职业禁忌证" v-bind="validateInfos.zySymptom">
            <j-select-multiple
              type="list_multi"
              v-model:value="formData.zySymptom"
              dictCode="zy_taboo_symptom,name,code"
              placeholder="请选择职业禁忌症"
              :disabled="disabled"
              :triggerChange="false"
            />
          </a-form-item>
        </a-col>
      </a-row>
    </a-form>

    <ZyAdviceDictModal ref="dictModal" @success="reloadDictList" />
  </a-spin>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, defineProps, inject, nextTick, onMounted, reactive, ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../ZyConclusionDetail.api';
  import { Form } from 'ant-design-vue';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JSelectMultiple from '@/components/Form/src/jeecg/components/JSelectMultiple.vue';
  import { ItemDict } from '#/types';
  import { match } from 'pinyin-pro';
  import { defaultValue, listZyAdvice } from '@/views/basicinfo/ZyConclusionDict.api';
  import { JAsyncSearchSelect } from '@/components/Form';
  import ZyAdviceDictModal from '@/views/occu/components/ZyAdviceDictModal.vue';

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: {
      type: Object,
      default: () => {},
    },
    formBpm: { type: Boolean, default: true },
  });
  let dictDefaultValue = {};
  const customerReg4Summary = inject('customerReg4Summary');
  const customerSummary = inject('customerSummary');
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    customerRegId: '',
    riskFactor: '',
    riskFactorId: '',
    workType: '',
    according: '',
    conclusion: '',
    advice: '',
    zyDisease: '',
    zySymptom: '',
    mainFlag: '0',
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    conclusion: [{ required: true, message: '请输入职业检结论', trigger: 'blur' }],
    according: [{ required: true, message: '请选择依据', trigger: 'blur' }],
    advice: [{ required: true, message: '请输入处理意见', trigger: 'blur' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(() => {
    if (props.formBpm === true) {
      if (props.formData.disabled === false) {
        return false;
      } else {
        return true;
      }
    }
    return props.formDisabled;
  });

  /**自动选项*/
  const dictInited = ref(false);
  const dictModal = ref();
  const dictList = ref([]);
  const filtedDictList = ref([]);
  const dictColumns = [
    {
      title: '字典值',
      dataIndex: 'content',
      key: 'content',
    },
  ];

  function setName(selectedOption) {
    if (selectedOption?.length > 0) {
      formData.riskFactor = selectedOption[0].text;
    }
  }

  function initDict() {
    if (!dictInited.value) {
      listZyAdvice().then((res) => {
        dictList.value = res;
        filtedDictList.value = res;
        dictInited.value = true;
      });
    }
  }

  function reloadDictList() {
    listZyAdvice().then((res) => {
      dictList.value = res;
      filtedDictList.value = res;
    });
  }

  function filterDict(e) {
    let value = e.target.value;
    if (!value) {
      filtedDictList.value = dictList.value;
      return;
    }
    //如果itemDictList不为空，且value不为空，过滤itemDictList
    if (dictList.value.length > 0 && value) {
      filtedDictList.value = dictList.value.filter((item) => {
        //console.log(item);
        let wordMatched = item.dictText.toLowerCase().includes(value.toLowerCase()) || item.helpChar?.toLowerCase().includes(value.toLowerCase());
        return !!wordMatched;
      });
    }
  }

  function click2UseItemDict(row: ItemDict) {
    return {
      onClick: () => {
        //向formData.advice追加内容，并去重
        if (formData.advice) {
          let adviceArr = formData.advice.split('，');
          if (adviceArr.indexOf(row.content) === -1) {
            formData.advice = formData.advice + '，' + row.content;
          }
        } else {
          formData.advice = row.content;
        }
        //formData.advice = formData.advice ? formData.advice + '，' + row.content : row.content;
        //formData.advice = row.content;
        //increaseUseCount({ id: row.id, count: 1 });
      },
    };
  }

  function handelAddItemDict() {
    //打开字典维护界面
    dictModal.value?.add({ dictText: formData.advice || '' });
  }

  function initDictDefault() {
    return new Promise((resolve, reject) => {
      if (dictDefaultValue && Object.keys(dictDefaultValue).length > 0) {
        resolve(dictDefaultValue);
      } else {
        defaultValue().then((res) => {
          dictDefaultValue = res;
          resolve(res);
        });
      }
    });
  }

  /**
   * 新增
   */
  function add(record) {
    record = record || {};
    edit(record);
    /* initDictDefault().then((res) => {
    record.according = dictDefaultValue['结论依据']?.dictText;
    record.conclusion = dictDefaultValue['检查结论']?.dictText;
    record.advice = dictDefaultValue['处理意见']?.dictText;
    edit(record);
  });*/
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      //赋值
      Object.assign(formData, record);
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    if (!formData.riskFactor) {
      createMessage.error('请选择危害因素');
      return;
    }
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    model.customerRegId = customerReg4Summary.value.id;
    model.summaryId = customerSummary.value.id;
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }

  onMounted(() => {
    initDict();
  });

  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    height: 500px !important;
    overflow-y: auto;
    padding: 14px;
  }
</style>
