:root{--cui-accent: #205F78;--cui-accent-hover: #0b455c;--cui-accent-semi-10: rgba(32, 95, 120, 0.1);--cui-accent-semi-40: rgba(32, 95, 120, 0.38);--cui-accent-semi-60: rgba(32, 95, 120, 0.62);--cui-accent-text: #205F78;--cui-accent-text-hover: #0b455c;--cui-accent-text-semi-10: rgba(32, 95, 120, 0.1);--cui-accent-text-semi-40: rgba(32, 95, 120, 0.38);--cui-accent-text-semi-60: rgba(32, 95, 120, 0.62);--cui-accent-icon: #205F78;--cui-accent-icon-hover: #0b455c;--cui-accent-secondary: #FAAB1C;--cui-accent-warning: #e59500;--cui-accent-warning-hover: #c78306;--cui-accent-warning-semi-10: rgba(229, 149, 0, 0.1);--cui-accent-warning-semi-40: rgba(229, 149, 0, 0.38);--cui-accent-warning-semi-60: rgba(229, 149, 0, 0.62);--cui-accent-warning-text: #e59500;--cui-accent-warning-text-hover: #c78306;--cui-accent-warning-text-semi-10: rgba(229, 149, 0, 0.1);--cui-accent-warning-text-semi-40: rgba(229, 149, 0, 0.38);--cui-accent-warning-text-semi-60: rgba(229, 149, 0, 0.62);--cui-accent-error: #be1f1f;--cui-accent-error-hover: #930f0f;--cui-accent-error-semi-10: rgba(190, 31, 31, 0.1);--cui-accent-error-semi-40: rgba(190, 31, 31, 0.38);--cui-accent-error-semi-60: rgba(190, 31, 31, 0.62);--cui-accent-error-text: #be1f1f;--cui-accent-error-text-hover: #930f0f;--cui-accent-error-text-semi-10: rgba(190, 31, 31, 0.1);--cui-accent-error-text-semi-40: rgba(190, 31, 31, 0.38);--cui-accent-error-text-semi-60: rgba(190, 31, 31, 0.62);--cui-contrast: #ffffff;--cui-contrast-semi-10: rgba(255, 255, 255, 0.1);--cui-contrast-semi-40: rgba(255, 255, 255, 0.38);--cui-contrast-semi-60: rgba(255, 255, 255, 0.62);--cui-contrast-text: #ffffff;--cui-contrast-text-semi-40: rgba(255, 255, 255, 0.38);--cui-bg-body: #E6E6E6;--cui-bg-body-overlay: rgba(230, 230, 230, 0.38);--cui-bg-panels: #f1f1f1;--cui-bg-panels-section: rgba(0, 0, 0, 0.075);--cui-bg-panels-border: #DCDCDC;--cui-bg-panels-overlay: rgba(241, 241, 241, 0.38);--cui-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.3);--cui-shadow-border: 0 0 5px 1px rgba(0, 0, 0, 0.1);--cui-overlay: rgba(0, 0, 0, 0.2);--cui-outline-offset: -2px;--cui-outline: 2px solid var(--cui-accent);--cui-outline-contrast: 2px solid var(--cui-contrast);--cui-outline-warning: 2px solid var(--cui-accent-warning);--cui-outline-error: 2px solid var(--cui-accent-error);--cui-text: #333333;--cui-text-semi-10: rgba(51, 51, 51, 0.1);--cui-text-semi-40: rgba(51, 51, 51, 0.38);--cui-text-semi-60: rgba(51, 51, 51, 0.62);--cui-text-family: "Open Sans", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;--cui-text-size: 12px;--cui-text-size-lg: 14px;--cui-text-size-xl: 18px;--cui-text-size-sm: 10px;--cui-border-radius: 4px;--cui-btn-bg: rgba(0, 0, 0, 0.075);--cui-btn-bg-hover: rgba(0, 0, 0, 0.12);--cui-btn-transparent-warning: rgba(194, 130, 12, 0.1);--cui-btn-transparent-warning-hover: rgba(194, 130, 12, 0.2);--cui-btn-transparent-error: rgba(147, 15, 15, 0.1);--cui-btn-transparent-error-hover: rgba(147, 15, 15, 0.15);--cui-btn-group-header-bg: #dddddd;--cui-btn-group-header-bg-hover: #c6c6c6;--cui-item-bg: rgba(0, 0, 0, 0.075);--cui-item-bg-hover: rgba(0, 0, 0, 0.12);--cui-input-bg: rgba(0, 0, 0, 0.075);--cui-input-bg-hover: rgba(0, 0, 0, 0.12);--cui-input-bg-focus: var(--cui-input-bg-hover);--cui-input-text: var(--cui-text);--cui-input-text-disabled: var(--cui-text-semi-40);--cui-input-text-placeholder: var(--cui-text-semi-40);--cui-dd-background: #ffffff;--cui-dd-background-hover: #ededed;--cui-dd-divider: #e0e0e0;--cui-binding-default: var(--cui-text-semi-60);--cui-binding-default-shadow: var(--cui-text-semi-40);--cui-binding-modified: #4dca7d;--cui-binding-modified-shadow: rgba(77, 202, 125, 0.62);--cui-binding-bind: #e4d50a;--cui-binding-bind-shadow: rgba(225, 212, 18, 0.62);--cui-binding-error: var(--cui-accent-error);--cui-binding-error-shadow: var(--cui-accent-error-semi-60);--cui-menu-splitter: rgba(255, 255, 255, 0.15);--cui-prop-editors-drag-shadow: 1px 2px 5px 0 rgba(0, 0, 0, 0.38);--cui-wizard-bg: var(--cui-accent-semi-60);--cui-wizard-dark-bg-main: rgba(0, 0, 0, 0.62);--cui-wizard-dark-bg-secondary: rgba(0, 0, 0, 0.2);--cui-wizard-dark-hover: rgba(0, 0, 0, 0.62);--cui-wizard-light-bg-main: rgba(255, 255, 255, 0.62);--cui-wizard-light-bg-secondary: rgba(255, 255, 255, 0.2);--cui-wizard-light-hover: rgba(255, 255, 255, 0.62);--cui-progressbar-bg: var(--cui-bg-panels);--cui-progressbar-bg-semi: var(--cui-btn-bg-hover);--cui-progress-fill-color: var(--cui-text);--cui-progress-value-color: var(--cui-contrast-text);--cui-scrollbar-color: rgba(0, 0, 0, 0.2);--cui-scrollbar-color-contrast: rgba(255, 255, 255, 0.2);--cui-treeview-outline-color: var(--cui-bg-panels-border);--cui-calendar-range-fill-color: var(--cui-accent-text-semi-40);--cui-notification-btn-accent-bg: var(--cui-accent);--cui-notification-btn-warning-bg: var(--cui-accent-warning);--cui-notification-btn-error-bg: var(--cui-accent-error);--cui-notification-btn-bg: rgba(0, 0, 0, .15);--cui-notification-btn-bg-hover: rgba(0, 0, 0, .25)}#open-report-dialog{top:0;left:0;width:0;height:0;opacity:0}.arjs-export-panel .gc-property-category{display:flex;flex-direction:column}.gcv-document-view .gcv-progress{position:absolute;z-index:100;width:100%}.gcv-pageview{width:100%;height:100%}.gcv-pages-container{width:100%;height:100%}.gcv-page-list{display:grid;min-height:100%;min-width:fit-content;gap:40px;justify-content:center}@container app (min-width: 768px){.gcv-pages-container--margins .gcv-page-list{padding:40px}}.gcv-page-list--align-left{justify-content:left}.gcv-page-list--align-right{justify-content:right}.gcv-page{position:relative;background-color:#fff}.gcv-page--border{border:1pt solid rgba(0,0,0,.05);box-shadow:none}.gcv-page--shadow{box-shadow:0 0 5px 2px rgba(0,0,0,.05)}.gcv-page-input{position:relative;width:100px;margin:0}.gcv-page-input>input[type=text].gc-input{vertical-align:top;background-color:transparent}.gcv-page-input>input[type=text].gc-input:not([disabled]):focus+.gcv-page-input__text{display:none}.gcv-page-input>input[type=text].gc-input[disabled]+.gcv-page-input__text{opacity:.38}.gcv-page-input__text{position:absolute;top:0;left:0;width:100%;height:40px;text-align:center;pointer-events:none;color:var(--cui-accent);font-size:var(--cui-text-size);line-height:40px}.gcv-header-container--fullscreen{position:absolute}.gcv-header-container--fullscreen .gcv-page-input{width:80px}.gcv-header-container--fullscreen .gcv-page-input__text{height:30px;font-size:var(--cui-text-size-sm);line-height:30px}.gc-viewer--small-ui-size .gcv-header-container .gcv-page-input__text,.gc-viewer--medium-ui-size .gcv-header-container .gcv-page-input__text{font-size:var(--cui-text-size);line-height:30px}.gcv-zoom{position:relative;width:auto;margin:0;border-radius:20px}.gcv-zoom>.gc-btn{position:absolute;top:0;left:0}.gcv-zoom>.gc-btn:last-of-type{right:0;left:auto}.gcv-zoom>.gc-dd>.gc-btn{padding-right:30px;padding-left:30px}.gcv-zoom__toggle{position:absolute;top:0;left:0;display:block;width:100%;height:40px;padding-top:20px;text-align:center;pointer-events:none;color:var(--cui-text);font-size:var(--cui-text-size);line-height:13.3333333333px}.gcv-header-container--fullscreen .gcv-zoom{border-radius:15px}.gcv-header-container--fullscreen .gcv-zoom>.gc-dd>.gc-btn{padding-right:25px;padding-left:25px}.gcv-header-container--fullscreen .gcv-zoom__toggle{height:30px;padding-top:15px;font-size:var(--cui-text-size);line-height:10px}.gc-viewer--small-ui-size .gcv-zoom__toggle,.gc-viewer--medium-ui-size .gcv-zoom__toggle{height:30px;padding-top:15px;font-size:var(--cui-text-size);line-height:10px}.gc-viewer--large-ui-size .gcv-header-container:not(.gcv-header-container--fullscreen) .gcv-zoom__toggle{padding-top:25px}.progress-modal{background-color:var(--cui-accent);color:var(--cui-contrast);position:absolute;left:25%;width:50%;height:180px;top:50%;transform:translateY(-50%);display:flex;flex-direction:column;outline:none;border-radius:var(--cui-border-radius);justify-content:center;align-items:center}.progress-modal .h3{display:block}.viewer-thumbnails{overflow:auto;width:100%;height:100%}.viewer-thumbnail{position:relative;z-index:1;width:214px;height:301px;margin:0 auto 45px auto;border:2px solid transparent}.viewer-thumbnail:hover{color:var(--cui-accent-hover);border:2px solid var(--cui-accent-hover)}.viewer-thumbnail--selected{color:var(--cui-accent);border:2px solid var(--cui-accent)}.viewer-thumbnail>div,.viewer-thumbnail>p{position:relative;z-index:0;overflow:hidden;width:210px;height:297px;pointer-events:none;background-color:#fff}.viewer-thumbnail>p{text-align:center;color:var(--cui-text);opacity:.38;font-size:80px;font-weight:bold;line-height:297px}.viewer-thumbnail>span{position:absolute;bottom:-30px;left:0;display:block;width:100%;text-align:center;font-size:12px;line-height:30px}.gc-viewer{display:flex;overflow:hidden;box-sizing:border-box;width:100%;height:100%;margin:0;padding:0;color:var(--cui-text);background-color:var(--cui-bg-body);font-family:var(--cui-text-family);container-type:size;container-name:app}.gc-viewer--medium-ui-size .gcv-header-container{height:41px}.gc-viewer--medium-ui-size .gc-sidebar__container{padding-top:40px}.gc-viewer--small-ui-size .gcv-header-container{height:31px;padding:0 5px}.gcv-menu{flex:0 0 auto;height:100%}.gcv-menu--hidden{display:none}@container app (max-width: 768px){.gcv-menu .gc-menu__panel-container--visible{width:calc(100cqw - 50px)}.gcv-menu .gc-menu__panel-container--visible .gc-menu__panel-container__content{width:calc(100cqw - 50px)}}.gcv-main-view{position:relative;display:grid;flex:1 1 auto;width:0;height:100%;transition:all .2s ease-in-out;grid-template-columns:minmax(0, 1fr) auto;grid-template-rows:auto auto minmax(0, 1fr);grid-template-areas:"header header" "toppanel sidebar" "view sidebar" "bottompanel sidebar";flex-wrap:nowrap;justify-content:flex-start;align-content:stretch;align-items:stretch}.gcv-main-view--fullscreen{height:100%}.gcv-main-view--fullscreen>.gcv-top-panel,.gcv-main-view--fullscreen .gcv-bottom-panel,.gcv-main-view--fullscreen .gcv-special-location-panel-controls{display:none}@container app (max-width: 768px){.gcv-main-view{width:calc(100cqw - 50px)}}.gcv-header-container{display:flex;width:100%;height:50px;border-bottom:1px solid var(--cui-bg-panels-border);background-color:var(--cui-bg-panels);align-items:center;grid-area:header}.gcv-header-container--hidden{display:none}.gcv-header-container--sidebar-hidden .gc-sidebar__menu{display:none}.gcv-header-container--fullscreen{position:fixed;z-index:1009;bottom:40px;left:50%;width:324px;height:40px;transform:translateX(-50%);opacity:1;border-bottom:0 solid transparent;border-radius:25px;background-color:var(--cui-bg-panels);grid-area:unset}.gcv-header-container--fullscreen>.gc-rv-inline-block:last-child{margin-right:0}.gcv-header-container>.gcv-toolbar{flex:1 1 auto;width:0}.gcv-header-container>.gcv-toolbar>.gcv-toolbar-wrapper{padding:5px}.gcv-sidebar{grid-area:sidebar}.gcv-sidebar--hidden{display:none}.gcv-sidebar--headless .gc-sidebar--collapsed .gc-sidebar__menu{visibility:hidden}.gcv-sidebar .gc-sidebar{z-index:1}.gcv-top-panel{border-bottom:1px solid #dbdbdb;background-color:var(--cui-bg-panels);grid-area:toppanel}.gcv-bottom-panel{border-top:1px solid #dbdbdb;background-color:var(--cui-bg-panels);grid-area:bottompanel}.gcv-special-location-panel-controls{display:inline-flex;height:inherit;padding:5px;align-items:center}.gcv-special-location-panel-controls--small-ui-size{padding:0 5px}.gcv-special-location-panel-controls--with-separator .gcv-special-location-panel-controls__separator{width:1px;height:50%;margin-left:5px;background-color:var(--cui-bg-panels-border)}.gcv-document-view{position:relative;z-index:1;overflow:hidden;width:100%;height:100%;grid-area:view;touch-action:none}.search{display:flex;flex-direction:column;box-sizing:border-box;width:100%;height:100%;padding:15px}.search>*:not(.search__results){flex:0 0 auto}.search .gc-input{width:100%;margin-bottom:5px}.search__query-params{display:flex;flex-wrap:wrap}.search__query-params>.gc-check{flex:1 1 auto;box-sizing:border-box;min-width:50%}.search__query-params>.gc-check .gc-check__label{white-space:nowrap}.search__results{overflow:auto;flex:1 1 auto;width:100%;margin:0;padding:0}.search__results>.gc-btn{width:100%;margin-top:10px}.search__results-header{display:block;width:100%;height:40px;margin:0;text-transform:uppercase;color:var(--cui-text);font-size:var(--cui-text-size);font-weight:bold;line-height:40px}.search-result{position:relative;display:block;max-height:60px;padding:0 10px;border-radius:var(--cui-border-radius);font-size:var(--cui-text-size);line-height:30px}.search-result:hover{cursor:pointer;user-select:none;background-color:var(--cui-input-bg-hover)}.search-result__text{display:block;overflow:hidden;overflow:hidden;max-height:60px;padding-right:15px;text-overflow:ellipsis}.search-result__text>i{color:var(--cui-accent);font-weight:bold}.search-result__page{position:absolute;top:0;right:0;display:block;width:30px;height:30px;text-align:center;color:var(--cui-text-semi-60)}.search__progress{position:relative;width:100%;height:50px;padding-top:10px}.search__progress>p{position:absolute;top:10px;left:0;display:block;overflow:hidden;width:100%;height:2px;margin:0;padding:0}.search__progress>p>span{display:block;height:100%;border-radius:2px;background-color:#fff}.gc-btn-group--align-justify .gc-btn.search__btn-search{flex:1 1 auto}.gc-btn-group--align-justify .gc-btn.search__btn-clear{flex:0 0 auto}.search__infomessage{text-align:center;color:var(--cui-text-semi-60);font-size:var(--cui-text-size);line-height:30px}@keyframes gcv_btn_icon_animation{0%{transform:rotate(0)}50%{transform:rotate(180deg)}100%{transform:rotate(360deg)}}.gcv-anim-icon{animation:gcv_btn_icon_animation .62s infinite linear}.gcv-thumb-vertical{width:2px;margin-left:2px;cursor:n-resize;border-radius:4px;background-color:rgba(0,0,0,.1)}.gcv-thumb-horizontal{height:2px;margin-top:2px;cursor:w-resize;border-radius:4px;background-color:rgba(0,0,0,.1)}.highlight{background-color:#ff8}.gcv-parameters__control{position:relative}.gcv-parameters__control:before{position:absolute;width:5px;height:5px;transform:translateY(-50%);border-radius:50%;background-color:var(--cui-accent-error)}.gcv-parameters__control--required-top-left:before{top:15px;left:-10px;content:""}.gcv-parameters__control--required-top-right:before{top:15px;right:-10px;content:""}.gcv-parameters__control--required-bottom-left:before{bottom:15px;left:-10px;content:"";transform:translateY(50%)}.gcv-parameters--layout-vertical{padding:0 15px}.gcv-parameters--layout-vertical .gcv-parameters__control{margin-bottom:15px}.gcv-parameters--layout-horizontal{position:relative;display:flex;padding:0 125px 0 15px;flex-wrap:wrap}.gcv-parameters--layout-horizontal .gcv-parameters__control{width:330px;margin:0 15px 15px 0}.gcv-parameters--layout-horizontal .gcv-parameters__control--preview{position:absolute;top:15px;right:15px;width:110px;margin:0}.gcv-parameters--free-form{position:relative}.gcv-parameters--free-form .gcv-parameters__control{position:absolute}.gcv-parameters__control>.gcv-label{display:-ms-grid;display:grid;height:100%;-ms-grid-columns:minmax(0, 1fr);-ms-grid-rows:30px minmax(0, 1fr);grid-template-columns:minmax(0, 1fr);grid-template-rows:30px minmax(0, 1fr);grid-template-areas:"label" "content"}.gcv-parameters__control>.gcv-label>.gc-label__label{width:100%;grid-area:label}.gcv-parameters__control>.gcv-label>.gc-label__content{width:100%;height:100%;grid-area:content}.gcv-parameters__control>.gcv-label.gc-label--position-left{-ms-grid-columns:1fr minmax(0, 2fr);-ms-grid-rows:100%;grid-template-columns:1fr minmax(0, 2fr);grid-template-rows:100%;grid-template-areas:"label content"}.gcv-parameters__control>.gcv-label.gc-label--position-right{-ms-grid-columns:minmax(0, 2fr) 1fr;-ms-grid-rows:100%;grid-template-columns:minmax(0, 2fr) 1fr;grid-template-rows:100%;grid-template-areas:"content label"}.gcv-parameters__control>.gcv-label.gc-label--position-bottom{-ms-grid-columns:1fr;-ms-grid-rows:minmax(0, 1fr) 30px;grid-template-columns:1fr;grid-template-rows:minmax(0, 1fr) 30px;grid-template-areas:"content" "label"}.gcv-nullable{position:relative;display:flex;width:100%;height:100%;align-items:flex-start}.gcv-nullable>.gc-check{display:flex;flex:0 0 auto;width:60px}.gcv-nullable__content{flex:1 1 auto;height:100%;margin-right:15px}.gcv-multivalue-textarea{width:100%;height:120px !important;white-space:nowrap}.gcv-textarea{width:100%;height:100%}.gcv-slider-container{display:flex;height:30px;align-items:center;justify-content:center}.gcv-slider-container--mode-X>.gcv-slider{width:100%}.gcv-number-editor{display:flex}.gcv-number-editor__button{flex:0 0 auto}.gcv-number-editor__button:first-of-type{border-top-right-radius:0;border-bottom-right-radius:0}.gcv-number-editor__button:last-of-type{border-top-left-radius:0;border-bottom-left-radius:0}.gcv-number-editor__input.gc-input{flex:1 1 auto;width:100%;text-align:center;border-radius:0}.gc-btn.disabled{opacity:.38}.gc-btn.disabled:focus{box-shadow:none}.gc-btn.disabled.gc-btn--level-warning:focus{box-shadow:none}.gc-btn.disabled.gc-btn--level-error:focus{box-shadow:none}.gcv-plain-text{overflow:hidden;width:100%;height:100%;margin:0;padding:0;color:var(--cui-text);font-size:var(--cui-text-size);line-height:20px}.gcv-plain-text--info{color:var(--cui-accent-text)}.gcv-plain-text--warning{color:var(--cui-accent-warning)}.gcv-plain-text--error{color:var(--cui-accent-error)}.gcv-tree{width:100%;height:100%}.gcv-tree-node{display:flex;width:100%;height:30px;align-items:stretch;align-content:stretch}.gcv-tree-node__toggle{overflow:hidden;width:30px;flex-grow:0;flex-shrink:0}.gcv-tree-node__control{overflow:hidden;flex-grow:1;flex-shrink:1}.gcv-tree-node-text{width:100%;height:30px;font-size:var(--cui-text-size);line-height:30px}.gcv-date-range--limited .gc-date-range-aside__body{pointer-events:none;opacity:.32}.gcv-custom-content{display:block;flex:0 0 auto;height:40px;padding:0 10px;text-align:center;color:var(--cui-text-semi-60);font-size:var(--cui-text-size);font-style:italic;line-height:38px}.gcv-date-time-range-input{margin-bottom:5px}.gcv-date-time-range{position:relative}.gcv-date-time-range .gc-input.gc-size-sm{padding-right:60px}.gcv-date-time-range-toggle{position:absolute;top:0;right:30px;width:30px}.gcv-date-time-range-dropdown{height:300px;background-color:var(--cui-accent)}.gcv-date-time-range-dropdown .gc-date-range-aside{width:250px;height:300px}:root{--cui-accent: #205F78;--cui-accent-hover: #0b455c;--cui-accent-semi-10: rgba(32, 95, 120, 0.1);--cui-accent-semi-40: rgba(32, 95, 120, 0.38);--cui-accent-semi-60: rgba(32, 95, 120, 0.62);--cui-accent-text: #205F78;--cui-accent-text-hover: #0b455c;--cui-accent-text-semi-10: rgba(32, 95, 120, 0.1);--cui-accent-text-semi-40: rgba(32, 95, 120, 0.38);--cui-accent-text-semi-60: rgba(32, 95, 120, 0.62);--cui-accent-icon: #205F78;--cui-accent-icon-hover: #0b455c;--cui-accent-secondary: #FAAB1C;--cui-accent-warning: #e59500;--cui-accent-warning-hover: #c78306;--cui-accent-warning-semi-10: rgba(229, 149, 0, 0.1);--cui-accent-warning-semi-40: rgba(229, 149, 0, 0.38);--cui-accent-warning-semi-60: rgba(229, 149, 0, 0.62);--cui-accent-warning-text: #e59500;--cui-accent-warning-text-hover: #c78306;--cui-accent-warning-text-semi-10: rgba(229, 149, 0, 0.1);--cui-accent-warning-text-semi-40: rgba(229, 149, 0, 0.38);--cui-accent-warning-text-semi-60: rgba(229, 149, 0, 0.62);--cui-accent-error: #be1f1f;--cui-accent-error-hover: #930f0f;--cui-accent-error-semi-10: rgba(190, 31, 31, 0.1);--cui-accent-error-semi-40: rgba(190, 31, 31, 0.38);--cui-accent-error-semi-60: rgba(190, 31, 31, 0.62);--cui-accent-error-text: #be1f1f;--cui-accent-error-text-hover: #930f0f;--cui-accent-error-text-semi-10: rgba(190, 31, 31, 0.1);--cui-accent-error-text-semi-40: rgba(190, 31, 31, 0.38);--cui-accent-error-text-semi-60: rgba(190, 31, 31, 0.62);--cui-contrast: #ffffff;--cui-contrast-semi-10: rgba(255, 255, 255, 0.1);--cui-contrast-semi-40: rgba(255, 255, 255, 0.38);--cui-contrast-semi-60: rgba(255, 255, 255, 0.62);--cui-contrast-text: #ffffff;--cui-contrast-text-semi-40: rgba(255, 255, 255, 0.38);--cui-bg-body: #E6E6E6;--cui-bg-body-overlay: rgba(230, 230, 230, 0.38);--cui-bg-panels: #f1f1f1;--cui-bg-panels-section: rgba(0, 0, 0, 0.075);--cui-bg-panels-border: #DCDCDC;--cui-bg-panels-overlay: rgba(241, 241, 241, 0.38);--cui-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.3);--cui-shadow-border: 0 0 5px 1px rgba(0, 0, 0, 0.1);--cui-overlay: rgba(0, 0, 0, 0.2);--cui-outline-offset: -2px;--cui-outline: 2px solid var(--cui-accent);--cui-outline-contrast: 2px solid var(--cui-contrast);--cui-outline-warning: 2px solid var(--cui-accent-warning);--cui-outline-error: 2px solid var(--cui-accent-error);--cui-text: #333333;--cui-text-semi-10: rgba(51, 51, 51, 0.1);--cui-text-semi-40: rgba(51, 51, 51, 0.38);--cui-text-semi-60: rgba(51, 51, 51, 0.62);--cui-text-family: "Open Sans", "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;--cui-text-size: 12px;--cui-text-size-lg: 14px;--cui-text-size-xl: 18px;--cui-text-size-sm: 10px;--cui-border-radius: 4px;--cui-btn-bg: rgba(0, 0, 0, 0.075);--cui-btn-bg-hover: rgba(0, 0, 0, 0.12);--cui-btn-transparent-warning: rgba(194, 130, 12, 0.1);--cui-btn-transparent-warning-hover: rgba(194, 130, 12, 0.2);--cui-btn-transparent-error: rgba(147, 15, 15, 0.1);--cui-btn-transparent-error-hover: rgba(147, 15, 15, 0.15);--cui-btn-group-header-bg: #dddddd;--cui-btn-group-header-bg-hover: #c6c6c6;--cui-item-bg: rgba(0, 0, 0, 0.075);--cui-item-bg-hover: rgba(0, 0, 0, 0.12);--cui-input-bg: rgba(0, 0, 0, 0.075);--cui-input-bg-hover: rgba(0, 0, 0, 0.12);--cui-input-bg-focus: var(--cui-input-bg-hover);--cui-input-text: var(--cui-text);--cui-input-text-disabled: var(--cui-text-semi-40);--cui-input-text-placeholder: var(--cui-text-semi-40);--cui-dd-background: #ffffff;--cui-dd-background-hover: #ededed;--cui-dd-divider: #e0e0e0;--cui-binding-default: var(--cui-text-semi-60);--cui-binding-default-shadow: var(--cui-text-semi-40);--cui-binding-modified: #4dca7d;--cui-binding-modified-shadow: rgba(77, 202, 125, 0.62);--cui-binding-bind: #e4d50a;--cui-binding-bind-shadow: rgba(225, 212, 18, 0.62);--cui-binding-error: var(--cui-accent-error);--cui-binding-error-shadow: var(--cui-accent-error-semi-60);--cui-menu-splitter: rgba(255, 255, 255, 0.15);--cui-prop-editors-drag-shadow: 1px 2px 5px 0 rgba(0, 0, 0, 0.38);--cui-wizard-bg: var(--cui-accent-semi-60);--cui-wizard-dark-bg-main: rgba(0, 0, 0, 0.62);--cui-wizard-dark-bg-secondary: rgba(0, 0, 0, 0.2);--cui-wizard-dark-hover: rgba(0, 0, 0, 0.62);--cui-wizard-light-bg-main: rgba(255, 255, 255, 0.62);--cui-wizard-light-bg-secondary: rgba(255, 255, 255, 0.2);--cui-wizard-light-hover: rgba(255, 255, 255, 0.62);--cui-progressbar-bg: var(--cui-bg-panels);--cui-progressbar-bg-semi: var(--cui-btn-bg-hover);--cui-progress-fill-color: var(--cui-text);--cui-progress-value-color: var(--cui-contrast-text);--cui-scrollbar-color: rgba(0, 0, 0, 0.2);--cui-scrollbar-color-contrast: rgba(255, 255, 255, 0.2);--cui-treeview-outline-color: var(--cui-bg-panels-border);--cui-calendar-range-fill-color: var(--cui-accent-text-semi-40);--cui-notification-btn-accent-bg: var(--cui-accent);--cui-notification-btn-warning-bg: var(--cui-accent-warning);--cui-notification-btn-error-bg: var(--cui-accent-error);--cui-notification-btn-bg: rgba(0, 0, 0, .15);--cui-notification-btn-bg-hover: rgba(0, 0, 0, .25)}.arjs-export-panel{padding:0 15px 15px}.arjs-export-panel>.gc-btn{margin-top:16pt}.progress-modal{position:absolute;top:50%;left:25%;display:flex;flex-direction:column;width:50%;height:180px;transform:translateY(-50%);color:var(--cui-contrast);border-radius:var(--cui-border-radius);outline:none;background-color:var(--cui-accent);justify-content:center;align-items:center}.progress-modal .h3{display:block}.ar-contents{padding:15px}.ar-toc-item>.gc-btn-group{display:flex;overflow:hidden;width:100%;height:30px;text-align:left;white-space:nowrap;text-transform:capitalize;text-overflow:ellipsis;color:var(--cui-text);border:0;border-radius:var(--cui-border-radius);outline:none;background:none;font-size:var(--cui-text-size);line-height:30px;fill:var(--cui-text)}.ar-toc-item>.gc-btn-group>.gc-btn--custom{overflow:hidden;flex:1 1 auto;height:30px;padding:0 10px;text-overflow:ellipsis}.ar-toc-item>.gc-btn-group>.gc-btn--custom:hover{cursor:pointer}.ar-toc-item>.gc-btn-group>.gc-btn--with-icon path{fill:inherit}.ar-toc-item>.gc-btn-group>.gc-btn--with-icon:hover{cursor:pointer}.ar-toc-item>.gc-btn-group:hover{color:#fff;background-color:var(--cui-accent-hover);font-weight:bold;fill:#fff}.ar-toc-item>.ar-toc-item-children{overflow:hidden;width:100%;padding-left:15px}.ar-toc-item--level-2 button{font-size:var(--cui-text-size-sm)}.ar-toc-item--level-3 button{font-size:var(--cui-text-size-sm);font-style:italic}.gc-btn.gc-size-sm .gc-btn__icon:not(.gc-icon--custom)>svg{width:16px;height:16px}.gc-btn.gc-size-lg .gc-btn__icon>svg{width:24px;height:24px}.gc-dd-menu__item>.gc-icon>svg{flex:0 0 auto}.gc-dd-menu__item.gc-size-sm>.gc-icon:not(.gc-icon--custom)>svg{width:16px;height:16px}.gc-dd-menu__item.gc-size-lg>.gc-icon:not(.gc-icon--custom)>svg{width:24px;height:24px}.gc-icon>svg{width:24px;height:24px}.gc-icon--small>svg{width:16px;height:16px}.gc-icon--large>svg{width:24px;height:24px}.gc-icon--core>svg{width:initial !important;height:initial !important}.gc-icon-color{transition:fill .2s ease-in-out,stroke .2s ease-in-out}.gc-icon-color--text{transition:fill .2s ease-in-out;fill:currentColor}.gc-icon-color--stroke-text{transition:stroke .2s ease-in-out;stroke:currentColor}.gc-icon-color--accent{transition:fill .2s ease-in-out;fill:var(--cui-accent-secondary)}.gc-icon-color--stroke-accent{transition:stroke .2s ease-in-out;stroke:var(--cui-accent-secondary)}.viewer-reports-list{overflow:auto;width:100%;height:100%;padding:10px}.viewer-reports-list-item>button{display:block;overflow:hidden;width:calc(100% - 10px);height:30px;margin:0 5px;padding:0 10px;text-align:left;white-space:nowrap;text-transform:capitalize;text-overflow:ellipsis;color:var(--cui-text);border:0;border-radius:15px;outline:none;background:none;font-size:var(--cui-text-size);line-height:30px}.viewer-reports-list-item>button:hover{cursor:pointer;color:var(--cui-contrast-text);background-color:var(--cui-accent-hover);font-weight:bold}