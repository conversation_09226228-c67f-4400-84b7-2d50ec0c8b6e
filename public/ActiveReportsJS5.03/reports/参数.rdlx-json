{"Name": "Report", "Width": "11cm", "ReportParameters": [{"DataType": "String", "DefaultValue": {"Values": ["986c80ec-732f-45e4-af2c-e6e5e36ed0d3"]}, "Name": "id", "Prompt": "id"}], "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "PaperOrientation", "Value": "Portrait"}], "Page": {"PageWidth": "21cm", "PageHeight": "29.7cm", "RightMargin": "2.5cm", "LeftMargin": "2.5cm", "TopMargin": "2.5cm", "BottomMargin": "2.5cm", "Columns": 1, "ColumnSpacing": "0cm"}, "DataSources": [{"Name": "DataSource", "ConnectionProperties": {"DataProvider": "JSON", "ConnectString": "endpoint="}}], "Body": {"Height": "1.5cm", "ReportItems": [{"Type": "table", "Name": "表格1", "DataSetName": "DataSet", "TableColumns": [{"Width": "3.667cm"}, {"Width": "3.667cm"}, {"Width": "3.666cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框1", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "document.id", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.667cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框2", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "document.type", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.667cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框3", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "document.title", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.666cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框4", "DataElementName": "document.id", "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"document.id\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.667cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框5", "DataElementName": "document.type", "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"document.type\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.667cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框6", "DataElementName": "document.title", "CanGrow": true, "KeepTogether": true, "Value": "=Fields.Item(\"document.title\").Value", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "3.666cm", "Height": "0.75cm"}}]}]}, "Left": "0cm", "Top": "0cm", "Width": "11cm", "Height": "1.5cm"}]}, "DataSets": [{"Name": "DataSet", "Fields": [{"Name": "document.id", "DataField": "document.id"}, {"Name": "document.type", "DataField": "document.type"}, {"Name": "document.title", "DataField": "document.title"}], "Query": {"DataSourceName": "DataSource", "CommandText": "=\"uri=http://localhost:51980/api/graphql?token=11A9FDFF82FE7D08029AFE61BDDA01FFC1A1F94D72DE06B5A184BC573B239CAE;method=POST;body={\"\"query\"\":\"\"query {document(id: \\\"\"\" & Parameters!id.Value & \"\\\"\") { id, type, title}}\"\"};jpath=$.*;Header$Content-Type=application/json\""}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}