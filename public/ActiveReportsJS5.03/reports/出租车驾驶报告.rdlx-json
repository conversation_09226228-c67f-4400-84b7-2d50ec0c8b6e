{"Name": "Frontstore.rdlx", "Width": "0in", "DocumentMap": {"Source": "All"}, "EmbeddedImages": [{"Name": "footer-phone", "ImageData": "iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAfpJREFUeNq8l00oRFEUx2d8UyhSClGW0jQREspGSSyUIjuTIllJ+Uj5SrJBahaUr0QpFj52JLKxYChEWdlZSAj5qOd/6kzdpnvnvTHvOvVr3pxz3vzvve/c8+44DcNwuN1ux3+az+dzRJjkVIFpUGq3eJTCHw1cYAvEglaQAZ7sElbN2Av2QSR/jwceO2csEy4ELSApIN4WZKBhCzvBpOAX47mgWpdwEygLkt+pQzgBTJjkl/EjsLWqPVy5MnsAMaALvNg949sgecsgBSzoWOpD8CrJuQCDdjcQUfgTrCr29LtOYbI5SQ4VXL5u4VNwFOBL5taZprtzDUh8OWCbK1ubMM14T+IvBvPc3bS9JLrBj8TfDPp0Cp+DGUVsBDToEibrBzcSPy31Ii+9FuEPXtoPSYz6+gbIVNybFaT9mgqTnYF2RYx+eIcHIVoJuOKOV/NXYbIlMK6I0fFoTTippIJakMjXtAXHhHhIwg6u5FlFrA6MgkpwyY/nS6iHXnAQuPRWhQ0++ngV8R7e++kgmwXF2iinUy3IC1XYL97BszNMHhudUuPAt5BLLbfgL8JiS20EbyZ5Th4AfT6DY7AbjjDZOijiqrdiU7zcj+EKk13z1hkSikm0exasAMNW/0lYtW8+nazwe9vFjWUTnChqwRZhv92B+lBu+BVgAOirV+Y3t0ZPAAAAAElFTkSuQmCC", "MIMEType": "image/png"}, {"Name": "treadstone-logo-full-color-rgb", "ImageData": "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", "MIMEType": "image/png"}], "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "PaperOrientation", "Value": "Portrait"}], "Page": {"PageWidth": "8.5in", "PageHeight": "11in", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0.5in"}, "DataSources": [{"Name": "DataSource1", "ConnectionProperties": {"DataProvider": "JSON", "ConnectString": "endpoint=UberDrives.json"}}], "ReportSections": [{"Type": "Continuous", "Name": "Dashboard", "Page": {"PageWidth": "8.3in", "PageHeight": "11.69in", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0.5in"}, "Width": "8.2675in", "PageHeader": {"Height": "0.9167in", "ReportItems": [{"Type": "textbox", "Name": "TextBox1", "ZIndex": 22, "DataElementName": "TextBox1", "KeepTogether": true, "Value": "出租车驾驶报告", "Style": {"FontFamily": "微软雅黑", "FontSize": "20pt", "FontWeight": "Medium", "PaddingLeft": "0.4in", "Color": "#3da7a8", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": ""}, "Top": "0.00834999in", "Width": "4.3438in", "Height": "0.9in"}, {"Type": "image", "Name": "Image4", "ZIndex": 30, "Source": "Embedded", "Value": "treadstone-logo-full-color-rgb", "Sizing": "FitProportional", "VerticalAlignment": "Middle", "Style": {"PaddingRight": "0.4in"}, "Left": "5.822917in", "Top": "0.27085in", "Width": "2.4446in", "Height": "0.375in"}], "Style": {"BackgroundColor": "#f1f1f1"}}, "Body": {"Height": "21.087cm", "ReportItems": [{"Type": "rectangle", "Name": "Container2", "ZIndex": 1, "KeepTogether": true, "ReportItems": [{"Type": "dvchart", "Name": "Chart2", "ZIndex": 1, "DataSetName": "DataSet1", "Style": {"FontSize": "8pt"}, "Header": {"Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "12pt", "PaddingBottom": "12pt"}, "TextStyle": {"FontFamily": "微软雅黑", "FontSize": "14pt"}, "Title": "乘车目的划分"}, "Footer": {"Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "4pt"}, "TextStyle": {"FontSize": "8pt"}}, "Palette": "Custom", "CustomPalette": ["MediumTurquoise", "Gold", "#ffcdaa", "#c7c7c7", "#bfecb8", "#c3ebec", "#ffec9d", "#ffb1ae", "#9ebac4", "#febbf7"], "Legend": {"Hidden": true, "Orientation": "Vertical", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "TextStyle": {"FontSize": "8pt"}, "TitleStyle": {"FontSize": "8pt"}}, "PlotArea": {"Axes": [{"Format": "d", "LabelAngle": -90, "LabelStyle": {"FontFamily": "Montserrat", "FontSize": "8pt", "Color": "<PERSON>", "PaddingTop": "12pt"}, "LineStyle": {"Border": {"Color": "LightGray", "Style": "Solid", "Width": "0.25pt"}}, "MajorTickStyle": {"Border": {"Width": "0.25pt"}}, "MinorGridStyle": {"Border": {"Width": "0.25pt"}}, "MinorTickStyle": {"Border": {"Width": "0.25pt"}}, "Plots": ["Plot1"], "TextStyle": {"FontSize": "8pt"}, "TitleStyle": {"FontFamily": "微软雅黑", "FontSize": "8pt", "Color": "<PERSON>", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}}, {"AxisLine": false, "AxisType": "Y", "Format": "f", "LabelStyle": {"FontFamily": "Montserrat", "FontSize": "8pt", "Color": "<PERSON>", "PaddingLeft": "6pt", "PaddingRight": "12pt"}, "LineStyle": {"Border": {"Color": "LightGray", "Style": "Solid", "Width": "0.25pt"}}, "MajorGrid": true, "MajorGridStyle": {"Border": {"Color": "LightGray", "Style": "Solid", "Width": "0.25pt"}}, "MajorTickStyle": {"Border": {"Color": "LightGray", "Width": "0.25pt"}}, "MinorGridStyle": {"Border": {"Color": "LightGray", "Width": "0.25pt"}}, "Plots": ["Plot1"], "TextStyle": {"FontSize": "8pt"}, "TitleStyle": {"FontFamily": "微软雅黑", "FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}}], "Legends": [{"Position": "Top", "Style": {"PaddingLeft": "12pt", "PaddingRight": "12pt", "PaddingBottom": "12pt"}, "TextStyle": {"FontFamily": "Montserrat", "FontSize": "9pt", "Color": "<PERSON>"}, "TitleStyle": {"FontSize": "8pt"}}]}, "Plots": [{"PlotName": "Plot1", "Encodings": {"Values": [{"Field": {"FieldType": "Simple", "Value": ["=Fields!Miles.Value"], "Captions": [""]}, "Aggregate": "Sum"}], "Category": {"Field": {"FieldType": "Simple", "Value": ["=Fields!Purpose.Value"], "Captions": [""]}, "Sort": "Descending", "SortingField": "=Fields!Category.Value"}, "Color": {"Field": {"FieldType": "Simple", "Value": ["=Fields!Category.Value"], "Captions": [""]}, "Aggregate": "List"}, "Shape": {"Field": {"FieldType": "Simple"}}, "Size": {"Field": {"FieldType": "Simple"}}}, "Config": {"LineStyle": {"Color": "Black"}, "Symbols": true, "Text": {"ConnectingLine": {"Border": {"Color": "LightGray"}}, "TextPosition": "Center", "Style": {"FontFamily": "Helvetica", "FontSize": "11pt"}}}, "PlotChartType": "Column"}], "Top": "9.763124cm", "Width": "4.0729in", "Height": "4.0417in"}, {"Type": "dvchart", "Name": "Chart1", "DataSetName": "DataSet1", "Style": {"FontSize": "8pt"}, "Bar": {"Width": 1}, "Header": {"Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "12pt", "PaddingBottom": "12pt"}, "TextStyle": {"FontFamily": "微软雅黑", "FontSize": "14pt"}, "Title": "行驶里程按类别划分"}, "Footer": {"Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "4pt"}, "TextStyle": {"FontSize": "8pt"}}, "Palette": "Custom", "CustomPalette": ["MediumTurquoise", "Gold", "DeepPink", "#c7c7c7", "#bfecb8", "#c3ebec", "#ffec9d", "#ffb1ae", "#9ebac4", "#febbf7"], "Legend": {"Orientation": "Vertical", "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "TextStyle": {"FontSize": "8pt"}, "TitleStyle": {"FontSize": "8pt"}}, "PlotArea": {"Axes": [{"Format": "d", "Labels": false, "LineStyle": {"Border": {"Color": "LightGray", "Style": "Solid"}}, "Plots": ["Plot1"], "Position": "None", "Scale": "Percentage", "TextStyle": {"FontSize": "8pt"}, "Title": "Year", "TitleStyle": {"FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}}, {"AxisLine": false, "AxisType": "Y", "Format": "f", "Labels": false, "LineStyle": {"Border": {"Color": "LightGray", "Style": "Solid"}}, "MajorGrid": true, "MajorGridStyle": {"Border": {"Color": "LightGray", "Style": "Solid"}}, "MajorTickStyle": {"Border": {"Color": "LightGray"}}, "MinorGridStyle": {"Border": {"Color": "LightGray"}}, "Plots": ["Plot1"], "Scale": "Percentage", "TextStyle": {"FontSize": "8pt"}, "TitleStyle": {"FontSize": "12pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}}], "Legends": [{"Position": "Top", "Style": {"PaddingLeft": "12pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "12pt"}, "TextStyle": {"FontFamily": "Montserrat", "FontSize": "9pt", "Color": "<PERSON>"}, "TitleStyle": {"FontFamily": "微软雅黑", "FontSize": "8pt"}}]}, "Plots": [{"PlotName": "Plot1", "Encodings": {"Values": [{"Field": {"FieldType": "Simple", "Value": ["=Fields!Miles.Value"], "Captions": [""]}, "Aggregate": "Sum"}], "Category": {"Field": {"FieldType": "Simple"}, "Sort": "Ascending", "SortingField": "=Fields!Year.Value"}, "Details": [{"Field": {"FieldType": "Simple", "Value": ["=Fields!Category.Value"], "Captions": [""]}, "Group": "<PERSON><PERSON>"}], "Color": {"Field": {"FieldType": "Simple", "Value": ["=Fields!Category.Value"], "Captions": [""]}, "Aggregate": "List"}, "Shape": {"Field": {"FieldType": "Simple"}}, "Size": {"Field": {"FieldType": "Simple"}}}, "Config": {"AxisMode": "Radial", "ClippingMode": "None", "InnerRadius": 0.5, "LineStyle": {"Color": "Black"}, "Opacity": 0.95, "Radial": true, "Symbols": true, "Text": {"ConnectingLine": {"Border": {"Color": "LightGray"}}, "Offset": 40, "OverlappingLabels": "Show", "TextPosition": "Inside", "Template": "{valueField.value}", "Style": {"FontFamily": "Montserrat", "FontSize": "9pt", "Color": "<PERSON><PERSON><PERSON><PERSON>"}}}, "PlotChartType": "Pie", "PlotChartSubtype": "Stacked"}], "Width": "4.0729in", "Height": "3.375in"}], "Left": "9.8425cm", "Top": "1.058333cm", "Width": "4.0729in", "Height": "20.029cm"}, {"Type": "table", "Name": "Table1", "DataSetName": "DataSet1", "DetailDataElementOutput": "Output", "Style": {"FontFamily": "Segoe UI"}, "TableColumns": [{"Width": "1.0059in"}, {"Width": "0.9362in"}, {"Width": "0.9121in"}], "Header": {"TableRows": [{"Height": "0.6245in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox7", "DataElementName": "TextBox7", "CanGrow": true, "KeepTogether": true, "Value": "月份统计", "Style": {"FontFamily": "微软雅黑", "FontSize": "14pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Left", "VerticalAlign": "Middle"}, "Width": "2.8542in", "Height": "0.6245in"}, "ColSpan": 3}, null, null]}, {"Height": "0.6191in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox10", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "月份", "Style": {"Border": {"Color": "LightGray"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "6pt", "PaddingRight": "4pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle"}, "Width": "1.0059in", "Height": "0.6191in"}}, {"Item": {"Type": "textbox", "Name": "TextBox19", "DataElementName": "TextBox2", "CanGrow": true, "KeepTogether": true, "Value": "里程数", "Style": {"Border": {"Color": "LightGray"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "6pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0.9362in", "Height": "0.6191in"}}, {"Item": {"Type": "textbox", "Name": "TextBox20", "DataElementName": "TextBox3", "CanGrow": true, "KeepTogether": true, "Value": "乘车总次数", "Style": {"Border": {"Color": "LightGray"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "6pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0.9121in", "Height": "0.6191in"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.5793in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox4", "DataElementName": "TextBox5", "Action": {"BookmarkLink": "=Month(Fields!StartDate.Value)"}, "CanGrow": true, "KeepTogether": true, "Value": "=MonthName(Month( Fields!StartDate.Value ))", "Style": {"Border": {"Color": "White"}, "BottomBorder": {"Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "6pt", "PaddingRight": "4pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#3da7a8", "Format": "g", "TextAlign": "Left", "TextDecoration": "Underline", "VerticalAlign": "Middle"}, "Width": "1.0059in", "Height": "0.5793in"}}, {"Item": {"Type": "textbox", "Name": "TextBox5", "DataElementName": "Start", "CanGrow": true, "KeepTogether": true, "Value": "=Sum(Fields!Miles.Value)", "Style": {"Border": {"Color": "White"}, "BottomBorder": {"Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "6pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0.9362in", "Height": "0.5793in"}}, {"Item": {"Type": "textbox", "Name": "TextBox6", "DataElementName": "TextBox6", "CanGrow": true, "KeepTogether": true, "Value": "=Count(Fields!StartDate.Value)", "Style": {"Border": {"Color": "White"}, "BottomBorder": {"Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "6pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "g", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0.9121in", "Height": "0.5793in"}}]}], "Group": {"Name": "Table1_Detail_Group", "GroupExpressions": ["=Month(Fields!StartDate.Value )"], "PageBreakDisabled": "false"}}, "Left": "1.084792cm", "Top": "0.9789583cm", "Width": "2.8542in", "Height": "1.8229in"}]}, "PageFooter": {"Height": "0.6in", "ReportItems": [{"Type": "rectangle", "Name": "Container1", "KeepTogether": true, "ReportItems": [{"Type": "textbox", "Name": "TextBox2", "DataElementName": "TextBox70", "CanGrow": true, "KeepTogether": true, "Value": "=\"© \" & Year(Now()) & \" GrapeCity, Inc. All Rights Reserved.\"", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Left": "0.327857in", "Width": "3.375in", "Height": "0.6in"}, {"Type": "image", "Name": "Image2", "ZIndex": 1, "Source": "Embedded", "Value": "footer-phone", "Sizing": "Fit", "Left": "3.825759in", "Top": "0.2in", "Width": "0.2in", "Height": "0.2in"}, {"Type": "image", "Name": "Image1", "ZIndex": 1, "Source": "Embedded", "Value": "footer-phone", "Sizing": "Fit", "Left": "5.278785in", "Top": "0.2in", "Width": "0.2in", "Height": "0.2in"}, {"Type": "textbox", "Name": "TextBox71", "DataElementName": "TextBox71", "CanGrow": true, "KeepTogether": true, "Value": "1800.858.2739", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle"}, "Left": "4.123378in", "Width": "1.0433in", "Height": "0.6in"}, {"Type": "textbox", "Name": "TextBox3", "DataElementName": "TextBox72", "CanGrow": true, "KeepTogether": true, "Value": "Pittsburgh, PA:************", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle"}, "Left": "5.578785in", "Width": "2.1in", "Height": "0.6in"}], "Width": "8.3in", "Height": "0.6in"}], "Style": {"BackgroundColor": "#f1f1f1"}}}, {"Type": "Continuous", "Name": "Report", "Page": {"PageWidth": "11in", "PageHeight": "8.5in", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0in"}, "Width": "10.6007in", "PageHeader": {"Height": "0.7709in", "ReportItems": [{"Type": "textbox", "Name": "TextBox26", "DataElementName": "TextBox1", "KeepTogether": true, "Value": "出租车驾驶报告", "Style": {"FontFamily": "微软雅黑", "FontSize": "20pt", "FontWeight": "Medium", "PaddingLeft": "0.4in", "Color": "#3da7a8", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": ""}, "Left": "1.085cm", "Top": "0.0167in", "Width": "11.033cm", "Height": "0.7542in"}, {"Type": "image", "Name": "Image6", "ZIndex": 1, "Source": "Embedded", "Value": "treadstone-logo-full-color-rgb", "Sizing": "FitProportional", "VerticalAlignment": "Middle", "Style": {"PaddingRight": "0.4in"}, "Left": "8.1554in", "Top": "0.2709in", "Width": "2.4446in", "Height": "0.375in"}], "Style": {"BackgroundColor": "#f1f1f1"}}, "Body": {"Height": "1.7719in", "ReportItems": [{"Type": "table", "Name": "Table2", "DataSetName": "DataSet1", "DetailDataElementOutput": "Output", "Style": {"FontFamily": "Segoe UI"}, "TableColumns": [{"Width": "1.5763in"}, {"Width": "1.5554in"}, {"Width": "1.7471in"}, {"Width": "1.5554in"}, {"Width": "1.002in"}, {"Width": "1.4382in"}, {"Width": "1.3263in"}], "TableGroups": [{"Group": {"Name": "Table2_Group1", "GroupExpressions": ["=Month(Fields!StartDate.Value)"], "PageBreak": "Between", "PageBreakDisabled": "false"}, "Header": {"TableRows": [{"Height": "0.4in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox27", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.5763in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox31", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.5554in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox32", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.7471in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox33", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.5554in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox34", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.002in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox35", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.4382in", "Height": "0.4in"}}, {"Item": {"Type": "textbox", "Name": "TextBox36", "CanGrow": true, "KeepTogether": true, "Style": {"PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "1.3263in", "Height": "0.4in"}}]}, {"Height": "0.4989in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox12", "Bookmark": "=Month(First(Fields!StartDate.Value))", "DataElementName": "TextBox12", "CanGrow": true, "KeepTogether": true, "Value": "=MonthName(Month(First(Fields!StartDate.Value)))", "Style": {"FontFamily": "微软雅黑", "FontSize": "14pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#3da7a8"}, "Width": "10.2007in", "Height": "0.4989in"}, "ColSpan": 7}, null, null, null, null, null, null]}, {"Height": "0.4885in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox13", "DataElementName": "TextBox24", "CanGrow": true, "KeepTogether": true, "Value": "开始时间", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Width": "1.5763in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox17", "DataElementName": "TextBox25", "CanGrow": true, "KeepTogether": true, "Value": "结束时间", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Width": "1.5554in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox18", "DataElementName": "TextBox26", "CanGrow": true, "KeepTogether": true, "Value": "起始地点", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Width": "1.7471in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox21", "DataElementName": "TextBox27", "CanGrow": true, "KeepTogether": true, "Value": "目的地", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Width": "1.5554in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox14", "DataElementName": "TextBox27", "CanGrow": true, "KeepTogether": true, "Value": "里程", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Left": "6.8542in", "Top": "0.9792in", "Width": "1.002in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox16", "DataElementName": "TextBox9", "CanGrow": true, "KeepTogether": true, "Value": "类型", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Left": "8.0729in", "Top": "1.0833in", "Width": "1.4382in", "Height": "0.4885in"}}, {"Item": {"Type": "textbox", "Name": "TextBox22", "DataElementName": "TextBox9", "CanGrow": true, "KeepTogether": true, "Value": "目的", "Style": {"Border": {"Color": "Gainsboro", "Width": "0.25pt"}, "BottomBorder": {"Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "<PERSON><PERSON><PERSON><PERSON>", "TextAlign": "Left", "VerticalAlign": "Middle", "BackgroundColor": "#f8f8f8"}, "Width": "1.3263in", "Height": "0.4885in"}}]}], "RepeatOnNewPage": true}, "PreventOrphanedFooter": false}], "Details": {"TableRows": [{"Height": "0.3845in", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox28", "DataElementName": "TextBox4", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!StartDate.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "g", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "1.5763in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox29", "DataElementName": "TextBox6", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!EndDate.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "g", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Left": "3.0104in", "Top": "2.3333in", "Width": "1.5554in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox30", "DataElementName": "Start", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Start.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "g", "TextAlign": "Left", "VerticalAlign": "Middle"}, "Width": "1.7471in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox8", "DataElementName": "Stop", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Stop.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "VerticalAlign": "Middle"}, "Width": "1.5554in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox9", "DataElementName": "<PERSON>", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Miles.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "12pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Left": "6.7083in", "Top": "1.5417in", "Width": "1.002in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox15", "DataElementName": "Category", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Category.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "12pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "VerticalAlign": "Middle"}, "Left": "8.2083in", "Top": "1.5313in", "Width": "1.4382in", "Height": "0.3845in"}}, {"Item": {"Type": "textbox", "Name": "TextBox11", "DataElementName": "Purpose", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Purpose.Value", "Style": {"BottomBorder": {"Color": "Gainsboro", "Style": "Solid", "Width": "0.25pt"}, "FontFamily": "微软雅黑", "FontSize": "9pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "VerticalAlign": "Middle"}, "Width": "1.3263in", "Height": "0.3845in"}}]}], "Group": {"PageBreakDisabled": "false"}}, "Left": "0.4in", "Width": "10.2007in", "Height": "1.7719in"}]}, "PageFooter": {"Height": "0.5in", "ReportItems": [{"Type": "textbox", "Name": "TextBox23", "DataElementName": "TextBox70", "CanGrow": true, "KeepTogether": true, "Value": "=\"© \" & Year(Now()) & \" GrapeCity, Inc. All Rights Reserved.\n\"", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Left": "0.4in", "Width": "4.4479in", "Height": "0.4125in"}, {"Type": "textbox", "Name": "TextBox25", "ZIndex": 1, "DataElementName": "TextBox70", "CanGrow": true, "KeepTogether": true, "Value": "Pittsburgh, PA:************", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "8.2675in", "Width": "2.3325in", "Height": "0.4125in"}, {"Type": "textbox", "Name": "TextBox24", "ZIndex": 2, "DataElementName": "TextBox70", "CanGrow": true, "KeepTogether": true, "Value": "1800.858.2739", "Style": {"FontFamily": "Montserrat", "FontWeight": "Light", "Color": "<PERSON>", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "6.5435in", "Width": "1.2083in", "Height": "0.4125in"}, {"Type": "image", "Name": "Image3", "ZIndex": 3, "Source": "Embedded", "Value": "footer-phone", "MIMEType": "image/png", "Sizing": "FitProportional", "Left": "15.821cm", "Top": "0.12in", "Width": "0.508cm", "Height": "0.2in"}, {"Type": "image", "Name": "Image5", "ZIndex": 4, "Source": "Embedded", "Value": "footer-phone", "MIMEType": "image/png", "Sizing": "FitProportional", "Left": "20.028cm", "Top": "0.13in", "Width": "0.508cm", "Height": "0.2in"}], "Style": {"BackgroundColor": "#f1f1f1"}}}], "DataSets": [{"Name": "DataSet1", "Fields": [{"Name": "StartDate", "DataField": "StartDate[Date|M/D/YYYY H:mm]"}, {"Name": "EndDate", "DataField": "EndDate[Date|M/D/YYYY H:mm]"}, {"Name": "Category", "DataField": "Category"}, {"Name": "Start", "DataField": "Start"}, {"Name": "Stop", "DataField": "Stop"}, {"Name": "<PERSON>", "DataField": "<PERSON>"}, {"Name": "Purpose", "DataField": "Purpose"}], "Query": {"DataSourceName": "DataSource1", "CommandText": "$.[*]"}, "Filters": [{"FilterExpression": "=Fields!Purpose.Value", "Operator": "NotEqual", "FilterValues": ["void"]}], "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}