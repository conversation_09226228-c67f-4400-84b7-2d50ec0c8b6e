{"Name": "Report", "Type": "report", "Width": "50.5cm", "DataSources": [{"Name": "DataSource1", "ConnectionProperties": {"ConnectString": "jsondata=[{\"CompanyName\":\"杭州悠可化妆品有限公司\",\"transactionType\":0,\"ExItemDouble1\":1.2573679035E11,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":1011.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":1255.0,\"PracticalWeek4\":2684.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":1.2573679035E11,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":1011.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":1255.0,\"SpendPractical4\":2684.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"杭州美巴科技有限公司\",\"transactionType\":0,\"ExItemDouble1\":4.24E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":4.24E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"杭州宁久微贸易有限公司\",\"transactionType\":0,\"ExItemDouble1\":1.040000148E9,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":1.040000148E9,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"杭州锐太供应链管理有限公司\",\"transactionType\":0,\"ExItemDouble1\":2.4E7,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":2.4E7,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"杭州悠美美妆有限公司\",\"transactionType\":0,\"ExItemDouble1\":1.00350015E9,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":1.00350015E9,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"杭州悠悦品牌管理有限公司\",\"transactionType\":0,\"ExItemDouble1\":1.19100015E9,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":1.19100015E9,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"嘉兴朗宁信息科技有限公司\",\"transactionType\":0,\"ExItemDouble1\":2.4E7,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":2.4E7,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"旎网电子商务（杭州）有限公司\",\"transactionType\":0,\"ExItemDouble1\":9.6800315E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":9.6800315E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"普埃供应链管理（上海）有限责任公司\",\"transactionType\":0,\"ExItemDouble1\":7.8500015E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":7.8500015E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"锐太（上海）供应链管理有限公司\",\"transactionType\":0,\"ExItemDouble1\":2.4E7,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":2.4E7,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"锐太信息科技（上海）有限公司\",\"transactionType\":0,\"ExItemDouble1\":2.4E7,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":2.4E7,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"上海悠旎品牌管理有限公司\",\"transactionType\":0,\"ExItemDouble1\":3.68E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":3.68E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"MARCO POLO COSMETIC E-COMMERCE LIMITED\",\"transactionType\":0,\"ExItemDouble1\":7.9700015E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":7.9700015E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0},{\"CompanyName\":\"UCO Cosmetic Limited\",\"transactionType\":0,\"ExItemDouble1\":4.8E8,\"ExItemDouble2\":0.0,\"ExItemDouble3\":0.0,\"ExItemDouble4\":0.0,\"ExItemDouble5\":0.0,\"PracticalWeek1\":0.0,\"PracticalWeek2\":0.0,\"PracticalWeek3\":0.0,\"PracticalWeek4\":0.0,\"PracticalWeek5\":0.0,\"SpendExItem1\":4.8E8,\"SpendExItem2\":0.0,\"SpendExItem3\":0.0,\"SpendExItem4\":0.0,\"SpendExItem5\":0.0,\"SpendPractical1\":0.0,\"SpendPractical2\":0.0,\"SpendPractical3\":0.0,\"SpendPractical4\":0.0,\"SpendPractical5\":0.0,\"Month\":\"10\",\"day\":\"29,31\",\"BeginBankDeposits1\":0.0,\"BeginBankDeposits2\":0.0,\"BeginBankDeposits3\":0.0,\"BeginBankDeposits4\":0.0,\"BeginBankDeposits5\":0.0}]", "DataProvider": "JSON", "IntegratedSecurity": false, "Prompt": ""}, "Transaction": false}], "DataSets": [{"AccentSensitivity": "Auto", "CaseSensitivity": "Auto", "Collation": "", "Fields": [{"Name": "CompanyID", "DataField": "CompanyID"}, {"Name": "CompanyName", "DataField": "CompanyName"}, {"Name": "ColleExItem1", "DataField": "ColleExItem1"}, {"Name": "ColleExItem2", "DataField": "ColleExItem2"}, {"Name": "ColleExItem3", "DataField": "ColleExItem3"}, {"Name": "ColleExItem4", "DataField": "ColleExItem4"}, {"Name": "ColleExItem5", "DataField": "ColleExItem5"}, {"Name": "SpendExItem1", "DataField": "SpendExItem1"}, {"Name": "SpendExItem2", "DataField": "SpendExItem2"}, {"Name": "SpendExItem3", "DataField": "SpendExItem3"}, {"Name": "SpendExItem4", "DataField": "SpendExItem4"}, {"Name": "SpendExItem5", "DataField": "SpendExItem5"}, {"Name": "CollePractical1", "DataField": "CollePractical1"}, {"Name": "CollePractical2", "DataField": "CollePractical2"}, {"Name": "CollePractical3", "DataField": "CollePractical3"}, {"Name": "CollePractical4", "DataField": "CollePractical4"}, {"Name": "CollePractical5", "DataField": "CollePractical5"}, {"Name": "SpendPractical1", "DataField": "SpendPractical1"}, {"Name": "SpendPractical2", "DataField": "SpendPractical2"}, {"Name": "SpendPractical3", "DataField": "SpendPractical3"}, {"Name": "SpendPractical4", "DataField": "SpendPractical4"}, {"Name": "SpendPractical5", "DataField": "SpendPractical5"}, {"Name": "BeginBankDeposits1", "DataField": "BeginBankDeposits1"}, {"Name": "BeginBankDeposits2", "DataField": "BeginBankDeposits2"}, {"Name": "BeginBankDeposits3", "DataField": "BeginBankDeposits3"}, {"Name": "BeginBankDeposits4", "DataField": "BeginBankDeposits4"}, {"Name": "BeginBankDeposits5", "DataField": "BeginBankDeposits5"}, {"Name": "Month", "DataField": "Month"}, {"Name": "day", "DataField": "day"}], "KanatypeSensitivity": "Auto", "WidthSensitivity": "Auto", "Query": {"CommandText": "$.*", "CommandType": "Text", "DataSourceName": "DataSource1", "Timeout": 0}, "Name": "数据集1"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}], "Page": {"BottomMargin": "0cm", "LeftMargin": "0cm", "PageHeight": "80cm", "PageWidth": "50.5cm", "PageOrientation": "Portrait", "RightMargin": "0cm", "TopMargin": "0cm", "ColumnSpacing": "0cm", "Columns": 1}, "Layers": [{"Type": "layer", "Name": "default"}], "ReportParameters": [{"Type": "reportparameter", "Prompt": "ApiUrl", "DataType": "String", "Nullable": true, "Hidden": true, "Name": "ApiUrl"}, {"Type": "reportparameter", "Prompt": "UserID", "DataType": "String", "Nullable": true, "Hidden": true, "Name": "UserID"}, {"Type": "reportparameter", "Prompt": "BatchNo", "DataType": "String", "Nullable": true, "Hidden": true, "Name": "BatchNo"}], "ConsumeContainerWhitespace": true, "Body": {"Name": "Body", "Type": "section", "Height": "17cm", "ReportItems": [{"ZIndex": 1, "Type": "table", "Top": "0cm", "Left": "0cm", "Width": "50.5cm", "Height": "17cm", "TableColumns": [{"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}, {"Type": "tablecolumn", "Width": "2.525cm"}], "Header": {"Type": "tableheader", "TableRows": [{"Type": "tablerow", "Height": "1cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "2cm", "CanGrow": true, "Value": "公司", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#91a8c0", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框124"}, "ColSpan": 1, "RowSpan": 2, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "2cm", "CanGrow": true, "Value": "预算项目", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#91a8c0", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框125"}, "ColSpan": 1, "RowSpan": 2, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "15.15cm", "Height": "1cm", "CanGrow": true, "Value": "计划", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框126"}, "ColSpan": 6, "RowSpan": 1, "AutoMergeMode": "Never"}, null, null, null, null, null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "15.15cm", "Height": "1cm", "CanGrow": true, "Value": "实际", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框134"}, "ColSpan": 6, "RowSpan": 1, "AutoMergeMode": "Never"}, null, null, null, null, null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "15.15cm", "Height": "1cm", "CanGrow": true, "Value": "差异", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框139"}, "ColSpan": 6, "RowSpan": 1, "AutoMergeMode": "Never"}, null, null, null, null, null]}, {"Type": "tablerow", "Height": "1cm", "TableCells": [null, null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第一周\n(\"+ ToString(First(Fields!Month.Value))+\".1-\"+ ToString(First(Fields!Month.Value))+\".7)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框136"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第二周\n(\"+ ToString(First(Fields!Month.Value))+\".8-\"+ ToString(First(Fields!Month.Value))+\".14)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框135"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第三周\n(\"+ ToString(First(Fields!Month.Value))+\".15-\"+ ToString(First(Fields!Month.Value))+\".21)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框143"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第四周\n(\"+ ToString(First(Fields!Month.Value))+\".22-\"+ ToString(First(Fields!Month.Value))+\".28)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框144"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第五周\"& chr(10)   &\nIIF(First(Fields!day.Value).Contains(\"2\"), \"(\"+ToString(First(Fields!Month.Value))+\".\"+ First(Fields!day.Value).Substring(0,2)+\"-\"+ ToString(First(Fields!Month.Value))+\".\"+First(Fields!day.Value).Substring(3,2)+\")\",\"\")", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框145"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "计划合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#dcf9fd", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框146"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第一周\n(\"+ ToString(First(Fields!Month.Value))+\".1-\"+ ToString(First(Fields!Month.Value))+\".7)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框156"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第二周\n(\"+ ToString(First(Fields!Month.Value))+\".8-\"+ ToString(First(Fields!Month.Value))+\".14)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框157"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第三周\n(\"+ ToString(First(Fields!Month.Value))+\".15-\"+ ToString(First(Fields!Month.Value))+\".21)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框158"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第四周\n(\"+ ToString(First(Fields!Month.Value))+\".22-\"+ ToString(First(Fields!Month.Value))+\".28)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框159"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第五周\"& chr(10)   &\nIIF(First(Fields!day.Value).Contains(\"2\"), \"(\"+ToString(First(Fields!Month.Value))+\".\"+ First(Fields!day.Value).Substring(0,2)+\"-\"+ ToString(First(Fields!Month.Value))+\".\"+First(Fields!day.Value).Substring(3,2)+\")\",\"\")", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框160"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "实际合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#f9f5e6", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框161"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第一周\n(\"+ ToString(First(Fields!Month.Value))+\".1-\"+ ToString(First(Fields!Month.Value))+\".7)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框140"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第二周\n(\"+ ToString(First(Fields!Month.Value))+\".8-\"+ ToString(First(Fields!Month.Value))+\".14)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框178"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第三周\n(\"+ ToString(First(Fields!Month.Value))+\".15-\"+ ToString(First(Fields!Month.Value))+\".21)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框179"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第四周\n(\"+ ToString(First(Fields!Month.Value))+\".22-\"+ ToString(First(Fields!Month.Value))+\".28)\"", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框180"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "=\"第五周\"& chr(10)   &\nIIF(First(Fields!day.Value).Contains(\"2\"), \"(\"+ToString(First(Fields!Month.Value))+\".\"+ First(Fields!day.Value).Substring(0,2)+\"-\"+ ToString(First(Fields!Month.Value))+\".\"+First(Fields!day.Value).Substring(3,2)+\")\",\"\")", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框181"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1cm", "CanGrow": true, "Value": "差异合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#ffeeee", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#000000", "ShrinkToFit": "=True"}, "Name": "文本框182"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "7.5cm", "CanGrow": true, "Value": "合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框21"}, "ColSpan": 1, "RowSpan": 5, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "期初银行存款+现金", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框41"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits1.Value=null,\"0\",ToString((Fields!BeginBankDeposits1.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框61"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits2.Value=null,\"0\",ToString((Fields!BeginBankDeposits2.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框81"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits3.Value=null,\"0\",ToString((Fields!BeginBankDeposits3.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框101"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits4.Value=null,\"0\",ToString((Fields!BeginBankDeposits4.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框102"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits5.Value=null,\"0\",ToString((Fields!BeginBankDeposits5.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框103"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits1.Value +0+\n Fields!BeginBankDeposits2.Value +0+\n Fields!BeginBankDeposits3.Value +0+\n Fields!BeginBankDeposits4.Value +0+\n Fields!BeginBankDeposits5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框104"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits1.Value=null,\"0\",ToString((Fields!BeginBankDeposits1.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框105"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits2.Value=null,\"0\",ToString((Fields!BeginBankDeposits2.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框106"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits3.Value=null,\"0\",ToString((Fields!BeginBankDeposits3.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框107"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits4.Value=null,\"0\",ToString((Fields!BeginBankDeposits4.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框108"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((IIF(Fields!BeginBankDeposits5.Value=null,\"0\",ToString((Fields!BeginBankDeposits5.Value))) - 0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框109"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits1.Value +0+\n Fields!BeginBankDeposits2.Value +0+\n Fields!BeginBankDeposits3.Value +0+\n Fields!BeginBankDeposits4.Value +0+\n Fields!BeginBankDeposits5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框110"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框111"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits2.Value - Fields!BeginBankDeposits2.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框112"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(([BeginBankDeposits3] - [BeginBankDeposits3]))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框1"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits4.Value - Fields!BeginBankDeposits4.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框114"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits5.Value - Fields!BeginBankDeposits5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框115"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框116"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "收款合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框2"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!ColleExItem1.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框3"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!ColleExItem2.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框4"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!ColleExItem3.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框5"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!ColleExItem4.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框6"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!ColleExItem5.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框7"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem1.Value + 0 + Fields!ColleExItem2.Value + 0 + Fields!ColleExItem3.Value + 0 +Fields!ColleExItem4.Value + 0 +Fields!ColleExItem5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框8"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!CollePractical1.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框9"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!CollePractical2.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框10"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!CollePractical3.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框11"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!CollePractical4.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框12"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!CollePractical5.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框13"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical1.Value + 0 +  Fields!CollePractical2.Value + 0 + Fields!CollePractical3.Value  + 0 + Fields!CollePractical4.Value + 0 + Fields!CollePractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框14"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem1.Value - Fields!CollePractical1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框15"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem2.Value - Fields!CollePractical2.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框16"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem3.Value - Fields!CollePractical3.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框17"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem4.Value - Fields!CollePractical4.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框18"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem5.Value - Fields!CollePractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框19"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框20"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "付款合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框22"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendExItem1.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框23"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendExItem2.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框24"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendExItem3.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框25"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendExItem4.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框26"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendExItem5.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框27"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem1.Value + 0 + Fields!SpendExItem2.Value  + 0 + Fields!SpendExItem3.Value + 0 + Fields!SpendExItem4.Value  + 0 + Fields!SpendExItem5.Value -0))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框28"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendPractical1.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框29"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendPractical2.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框30"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendPractical3.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框31"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendPractical4.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框32"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(Fields!SpendPractical5.Value -0)", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框33"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendPractical1.Value + 0 + Fields!SpendPractical2.Value + 0 + Fields!SpendPractical3.Value + 0 + Fields!SpendPractical4.Value + 0 +  Fields!SpendPractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框34"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem1.Value - Fields!SpendPractical1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框35"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem2.Value - Fields!SpendPractical2.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框36"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem3.Value - Fields!SpendPractical3.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框37"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem4.Value - Fields!SpendPractical4.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框38"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem5.Value - Fields!SpendPractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框39"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框40"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "总收支差额", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框42"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem1.Value - Fields!SpendExItem1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框43"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem2.Value - Fields!SpendExItem2.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框44"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem3.Value - Fields!SpendExItem3.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框45"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem4.Value - Fields!SpendExItem4.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框46"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!ColleExItem5.Value - Fields!SpendExItem5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框47"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value + 0 + Fields!ColleExItem2.Value + 0 + Fields!ColleExItem3.Value + 0 +Fields!ColleExItem4.Value + 0 +Fields!ColleExItem5.Value) - (Fields!SpendExItem1.Value + 0 + Fields!SpendExItem2.Value  + 0 + Fields!SpendExItem3.Value + 0 + Fields!SpendExItem4.Value  + 0 + Fields!SpendExItem5.Value ))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框48"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical1.Value - Fields!SpendPractical1.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框49"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical2.Value - Fields!SpendPractical2.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框50"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical3.Value - Fields!SpendPractical3.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框51"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical4.Value - Fields!SpendPractical4.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框52"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((Fields!CollePractical5.Value - Fields!SpendPractical5.Value))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框53"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!CollePractical1.Value + 0 +  Fields!CollePractical2.Value + 0 + Fields!CollePractical3.Value  + 0 + Fields!CollePractical4.Value + 0 + Fields!CollePractical5.Value) -  (Fields!SpendPractical1.Value + 0 + Fields!SpendPractical2.Value + 0 + Fields!SpendPractical3.Value + 0 + Fields!SpendPractical4.Value + 0 +  Fields!SpendPractical5.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框54"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value - Fields!CollePractical1.Value) - (Fields!SpendExItem1.Value - Fields!SpendPractical1.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框55"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem2.Value - Fields!CollePractical2.Value) - (Fields!SpendExItem2.Value - Fields!SpendPractical2.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框56"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem3.Value - Fields!CollePractical3.Value) - (Fields!SpendExItem3.Value - Fields!SpendPractical3.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框57"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem4.Value - Fields!CollePractical4.Value) - (Fields!SpendExItem4.Value - Fields!SpendPractical4.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框58"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem5.Value - Fields!CollePractical5.Value) - (Fields!SpendExItem5.Value - Fields!SpendPractical5.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框59"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value) - \n(Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框60"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "期末银行存款+现金", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框82"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value - Fields!SpendExItem1.Value)+0+(Fields!BeginBankDeposits1.Value))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框83"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem2.Value - Fields!SpendExItem2.Value)+0+(Fields!BeginBankDeposits2.Value))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框84"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem3.Value - Fields!SpendExItem3.Value)+0+(Fields!BeginBankDeposits3.Value))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框85"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem4.Value - Fields!SpendExItem4.Value)+0+(Fields!BeginBankDeposits4.Value))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框86"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem5.Value - Fields!SpendExItem5.Value)+0+(Fields!BeginBankDeposits5.Value))))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框87"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((\n(Fields!ColleExItem1.Value - Fields!SpendExItem1.Value)+0+(Fields!BeginBankDeposits1.Value) +0 +\n(Fields!ColleExItem2.Value - Fields!SpendExItem2.Value)+0+(Fields!BeginBankDeposits2.Value)\n+ 0+ \n(Fields!ColleExItem3.Value - Fields!SpendExItem3.Value)+0+(Fields!BeginBankDeposits3.Value)\n+ 0+\n(Fields!ColleExItem4.Value - Fields!SpendExItem4.Value)+0+(Fields!BeginBankDeposits4.Value)  \n+ 0+\n(Fields!ColleExItem5.Value - Fields!SpendExItem5.Value)+0+(Fields!BeginBankDeposits5.Value) \n)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框88"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((Fields!CollePractical1.Value - Fields!SpendPractical1.Value)+ 0 +\n (Fields!BeginBankDeposits1.Value)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框89"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((Fields!CollePractical2.Value - Fields!SpendPractical2.Value)+ 0 +\n (Fields!BeginBankDeposits2.Value)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框90"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((Fields!CollePractical3.Value - Fields!SpendPractical3.Value)+ 0 +\n (Fields!BeginBankDeposits3.Value)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框91"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((Fields!CollePractical4.Value - Fields!SpendPractical4.Value)+ 0 +\n (Fields!BeginBankDeposits4.Value)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框92"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((Fields!CollePractical5.Value - Fields!SpendPractical5.Value)+ 0 +\n (Fields!BeginBankDeposits5.Value)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框93"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum(((\n(Fields!CollePractical1.Value - Fields!SpendPractical1.Value)+0+(Fields!BeginBankDeposits1.Value) +0 +\n(Fields!CollePractical2.Value - Fields!SpendPractical2.Value)+0+(Fields!BeginBankDeposits2.Value)\n+ 0+ \n(Fields!CollePractical3.Value - Fields!SpendPractical3.Value)+0+(Fields!BeginBankDeposits3.Value)\n+ 0+\n(Fields!CollePractical4.Value - Fields!SpendPractical4.Value)+0+(Fields!BeginBankDeposits4.Value)  \n+ 0+\n(Fields!CollePractical5.Value - Fields!SpendPractical5.Value)+0+(Fields!BeginBankDeposits5.Value) \n)))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框94"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value - Fields!CollePractical1.Value) - (Fields!SpendExItem1.Value - Fields!SpendPractical1.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框95"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem2.Value - Fields!CollePractical2.Value) - (Fields!SpendExItem2.Value - Fields!SpendPractical2.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框96"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem3.Value - Fields!CollePractical3.Value) - (Fields!SpendExItem3.Value - Fields!SpendPractical3.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框97"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem4.Value - Fields!CollePractical4.Value) - (Fields!SpendExItem4.Value - Fields!SpendPractical4.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框98"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem5.Value - Fields!CollePractical5.Value) - (Fields!SpendExItem5.Value - Fields!SpendPractical5.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框99"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Sum((((Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value) - \n(Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value) )))", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "BackgroundColor": "#e9faf0", "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框100"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}], "RepeatOnNewPage": true}, "Details": {"Type": "tabledetails", "TableRows": [{"Type": "tablerow", "Height": "1.5cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "7.5cm", "CanGrow": true, "Value": "=Fields!CompanyName.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "ShrinkToFit": "=True"}, "Name": "文本框127"}, "ColSpan": 1, "RowSpan": 5, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "期初银行存款+现金", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框63"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits1.Value=null,\"0\",ToString((Fields!BeginBankDeposits1.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框64"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits2.Value=null,\"0\",ToString((Fields!BeginBankDeposits2.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框65"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits3.Value=null,\"0\",ToString((Fields!BeginBankDeposits3.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框66"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits4.Value=null,\"0\",ToString((Fields!BeginBankDeposits4.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框67"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits5.Value=null,\"0\",ToString((Fields!BeginBankDeposits5.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框68"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value +0+\n Fields!BeginBankDeposits2.Value +0+\n Fields!BeginBankDeposits3.Value +0+\n Fields!BeginBankDeposits4.Value +0+\n Fields!BeginBankDeposits5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框69"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits1.Value=null,\"0\",ToString((Fields!BeginBankDeposits1.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框70"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits2.Value=null,\"0\",ToString((Fields!BeginBankDeposits2.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框71"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits3.Value=null,\"0\",ToString((Fields!BeginBankDeposits3.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框72"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits4.Value=null,\"0\",ToString((Fields!BeginBankDeposits4.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框73"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=IIF(Fields!BeginBankDeposits5.Value=null,\"0\",ToString((Fields!BeginBankDeposits5.Value))) - 0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框74"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value +0+\n Fields!BeginBankDeposits2.Value +0+\n Fields!BeginBankDeposits3.Value +0+\n Fields!BeginBankDeposits4.Value +0+\n Fields!BeginBankDeposits5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框75"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框76"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框77"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框78"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框79"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框80"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!BeginBankDeposits1.Value - Fields!BeginBankDeposits1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "Format": "n2", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框113"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "收款合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "ShrinkToFit": "=True"}, "Name": "文本框128"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem1.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框129"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem2.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框137"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem3.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框147"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem4.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框148"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem5.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框149"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem1.Value + 0 + Fields!ColleExItem2.Value + 0 + Fields!ColleExItem3.Value + 0 +Fields!ColleExItem4.Value + 0 +Fields!ColleExItem5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框150"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical1.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框162"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical2.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框163"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical3.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框164"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical4.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框165"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical5.Value -0", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框166"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!CollePractical1.Value + 0 +  Fields!CollePractical2.Value + 0 + Fields!CollePractical3.Value  + 0 + Fields!CollePractical4.Value + 0 + Fields!CollePractical5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框167"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem1.Value - Fields!CollePractical1.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框141"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem2.Value - Fields!CollePractical2.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框183"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem3.Value - Fields!CollePractical3.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框184"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem4.Value - Fields!CollePractical4.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框185"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem5.Value - Fields!CollePractical5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框186"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "=Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框187"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "付款合计", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "ShrinkToFit": "=True"}, "Name": "文本框131"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框132", "Value": "=Fields!SpendExItem1.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框138", "Value": "=Fields!SpendExItem2.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框142", "Value": "=Fields!SpendExItem3.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框151", "Value": "=Fields!SpendExItem4.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框152", "Value": "=Fields!SpendExItem5.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框153", "Value": "=Fields!SpendExItem1.Value + 0 + Fields!SpendExItem2.Value  + 0 + Fields!SpendExItem3.Value + 0 + Fields!SpendExItem4.Value  + 0 + Fields!SpendExItem5.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框154", "Value": "=Fields!SpendPractical1.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框155", "Value": "=Fields!SpendPractical2.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框168", "Value": "=Fields!SpendPractical3.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框169", "Value": "=Fields!SpendPractical4.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框170", "Value": "=Fields!SpendPractical5.Value -0"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框171", "Value": "=Fields!SpendPractical1.Value + 0 + Fields!SpendPractical2.Value + 0 + Fields!SpendPractical3.Value + 0 + Fields!SpendPractical4.Value + 0 +  Fields!SpendPractical5.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框172", "Value": "=Fields!SpendExItem1.Value - Fields!SpendPractical1.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框173", "Value": "=Fields!SpendExItem2.Value - Fields!SpendPractical2.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框174", "Value": "=Fields!SpendExItem3.Value - Fields!SpendPractical3.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框175", "Value": "=Fields!SpendExItem4.Value - Fields!SpendPractical4.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框176", "Value": "=Fields!SpendExItem5.Value - Fields!SpendPractical5.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框177", "Value": "=Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "总收支差额", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "ShrinkToFit": "=True"}, "Name": "文本框189"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框190", "Value": "=Fields!ColleExItem1.Value - Fields!SpendExItem1.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框191", "Value": "=Fields!ColleExItem2.Value - Fields!SpendExItem2.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框192", "Value": "=Fields!ColleExItem3.Value - Fields!SpendExItem3.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框193", "Value": "=Fields!ColleExItem4.Value - Fields!SpendExItem4.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框194", "Value": "=Fields!ColleExItem5.Value - Fields!SpendExItem5.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框195", "Value": "=((Fields!ColleExItem1.Value + 0 + Fields!ColleExItem2.Value + 0 + Fields!ColleExItem3.Value + 0 +Fields!ColleExItem4.Value + 0 +Fields!ColleExItem5.Value) - (Fields!SpendExItem1.Value + 0 + Fields!SpendExItem2.Value  + 0 + Fields!SpendExItem3.Value + 0 + Fields!SpendExItem4.Value  + 0 + Fields!SpendExItem5.Value ))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框196", "Value": "=Fields!CollePractical1.Value - Fields!SpendPractical1.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框197", "Value": "=Fields!CollePractical2.Value - Fields!SpendPractical2.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框198", "Value": "=Fields!CollePractical3.Value - Fields!SpendPractical3.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框199", "Value": "=Fields!CollePractical4.Value - Fields!SpendPractical4.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框200", "Value": "=Fields!CollePractical5.Value - Fields!SpendPractical5.Value"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框201", "Value": "=((Fields!CollePractical1.Value + 0 +  Fields!CollePractical2.Value + 0 + Fields!CollePractical3.Value  + 0 + Fields!CollePractical4.Value + 0 + Fields!CollePractical5.Value) -  (Fields!SpendPractical1.Value + 0 + Fields!SpendPractical2.Value + 0 + Fields!SpendPractical3.Value + 0 + Fields!SpendPractical4.Value + 0 +  Fields!SpendPractical5.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框202", "Value": "=((Fields!ColleExItem1.Value - Fields!CollePractical1.Value) - (Fields!SpendExItem1.Value - Fields!SpendPractical1.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框203", "Value": "=((Fields!ColleExItem2.Value - Fields!CollePractical2.Value) - (Fields!SpendExItem2.Value - Fields!SpendPractical2.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框204", "Value": "=((Fields!ColleExItem3.Value - Fields!CollePractical3.Value) - (Fields!SpendExItem3.Value - Fields!SpendPractical3.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框205", "Value": "=((Fields!ColleExItem4.Value - Fields!CollePractical4.Value) - (Fields!SpendExItem4.Value - Fields!SpendPractical4.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框206", "Value": "=((Fields!ColleExItem5.Value - Fields!CollePractical5.Value) - (Fields!SpendExItem5.Value - Fields!SpendPractical5.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "ShrinkToFit": "=True"}, "Name": "文本框207", "Value": "=((Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value) - \n(Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}, {"Type": "tablerow", "Height": "1.5cm", "TableCells": [null, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Value": "期末银行存款+现金", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框229"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框230", "Value": "=((Fields!ColleExItem1.Value - Fields!SpendExItem1.Value)+0+(Fields!BeginBankDeposits1.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框231", "Value": "=((Fields!ColleExItem2.Value - Fields!SpendExItem2.Value)+0+(Fields!BeginBankDeposits2.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框232", "Value": "=((Fields!ColleExItem3.Value - Fields!SpendExItem3.Value)+0+(Fields!BeginBankDeposits3.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框233", "Value": "=((Fields!ColleExItem4.Value - Fields!SpendExItem4.Value)+0+(Fields!BeginBankDeposits4.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框234", "Value": "=((Fields!ColleExItem5.Value - Fields!SpendExItem5.Value)+0+(Fields!BeginBankDeposits5.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框235", "Value": "=(\n(Fields!ColleExItem1.Value - Fields!SpendExItem1.Value)+0+(Fields!BeginBankDeposits1.Value) +0 +\n(Fields!ColleExItem2.Value - Fields!SpendExItem2.Value)+0+(Fields!BeginBankDeposits2.Value)\n+ 0+ \n(Fields!ColleExItem3.Value - Fields!SpendExItem3.Value)+0+(Fields!BeginBankDeposits3.Value)\n+ 0+\n(Fields!ColleExItem4.Value - Fields!SpendExItem4.Value)+0+(Fields!BeginBankDeposits4.Value)  \n+ 0+\n(Fields!ColleExItem5.Value - Fields!SpendExItem5.Value)+0+(Fields!BeginBankDeposits5.Value) \n)"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框236", "Value": "=(Fields!CollePractical1.Value - Fields!SpendPractical1.Value)+ 0 +\n (Fields!BeginBankDeposits1.Value)"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框237", "Value": "=((Fields!CollePractical2.Value - Fields!SpendPractical2.Value)+0+(Fields!BeginBankDeposits2.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框238", "Value": "=((Fields!CollePractical3.Value - Fields!SpendPractical3.Value)+0+(Fields!BeginBankDeposits3.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框239", "Value": "=((Fields!CollePractical4.Value - Fields!SpendPractical4.Value)+0+(Fields!BeginBankDeposits4.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框240", "Value": "=((Fields!CollePractical5.Value - Fields!SpendPractical5.Value)+0+(Fields!BeginBankDeposits5.Value))"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框241", "Value": "=(\n(Fields!CollePractical1.Value - Fields!SpendPractical1.Value)+0+(Fields!BeginBankDeposits1.Value) +0 +\n(Fields!CollePractical2.Value - Fields!SpendPractical2.Value)+0+(Fields!BeginBankDeposits2.Value)\n+ 0+ \n(Fields!CollePractical3.Value - Fields!SpendPractical3.Value)+0+(Fields!BeginBankDeposits3.Value)\n+ 0+\n(Fields!CollePractical4.Value - Fields!SpendPractical4.Value)+0+(Fields!BeginBankDeposits4.Value)  \n+ 0+\n(Fields!CollePractical5.Value - Fields!SpendPractical5.Value)+0+(Fields!BeginBankDeposits5.Value) \n)"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框242", "Value": "=((Fields!ColleExItem1.Value - Fields!CollePractical1.Value) - (Fields!SpendExItem1.Value - Fields!SpendPractical1.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框243", "Value": "=((Fields!ColleExItem2.Value - Fields!CollePractical2.Value) - (Fields!SpendExItem2.Value - Fields!SpendPractical2.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框244", "Value": "=((Fields!ColleExItem3.Value - Fields!CollePractical3.Value) - (Fields!SpendExItem3.Value - Fields!SpendPractical3.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框245", "Value": "=((Fields!ColleExItem4.Value - Fields!CollePractical4.Value) - (Fields!SpendExItem4.Value - Fields!SpendPractical4.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框246", "Value": "=((Fields!ColleExItem5.Value - Fields!CollePractical5.Value) - (Fields!SpendExItem5.Value - Fields!SpendPractical5.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "2.525cm", "Height": "1.5cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Right", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Format": "n2", "BackgroundColor": "#e9faf0", "ShrinkToFit": "=True"}, "Name": "文本框247", "Value": "=((Fields!ColleExItem1.Value - Fields!CollePractical1.Value + 0 +\n Fields!ColleExItem2.Value - Fields!CollePractical2.Value + 0 +\n Fields!ColleExItem3.Value - Fields!CollePractical3.Value + 0 +\n Fields!ColleExItem4.Value - Fields!CollePractical4.Value + 0 +\nFields!ColleExItem5.Value - Fields!CollePractical5.Value) - \n(Fields!SpendExItem1.Value - Fields!SpendPractical1.Value + 0 +\n Fields!SpendExItem2.Value - Fields!SpendPractical2.Value + 0 +\n Fields!SpendExItem3.Value - Fields!SpendPractical3.Value + 0 +\n Fields!SpendExItem4.Value - Fields!SpendPractical4.Value + 0 +\n Fields!SpendExItem5.Value  - Fields!SpendPractical5.Value) )"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}], "Group": {"Name": ""}}, "Style": {"FontFamily": "微软雅黑"}, "Name": "表格2", "DataSetName": "数据集1", "FrozenRows": 2, "FrozenColumns": 2}]}, "TransformationInfo": []}