{"Type": "report", "Width": "13.5cm", "DataSources": [{"Name": "DataSource1", "ConnectionProperties": {"DataProvider": "JSON", "ConnectString": "=\"jsondoc=http://jsonplaceholder.typicode.com/comments?id=\" & Parameters!P1.Value"}}], "DataSets": [{"AccentSensitivity": "Auto", "CaseSensitivity": "Auto", "Collation": "", "Fields": [{"Name": "postId", "DataField": "postId"}, {"Name": "id", "DataField": "id"}, {"Name": "name", "DataField": "name"}, {"Name": "email", "DataField": "email"}, {"Name": "body", "DataField": "body"}], "KanatypeSensitivity": "Auto", "WidthSensitivity": "Auto", "Query": {"CommandText": "$.[*]", "CommandType": "Text", "DataSourceName": "DataSource1", "Timeout": 0}, "Name": "数据集1"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}], "Page": {"BottomMargin": "1cm", "LeftMargin": "1cm", "PageHeight": "29.7cm", "PageWidth": "21cm", "PageOrientation": "Portrait", "RightMargin": "1cm", "TopMargin": "1cm", "ColumnSpacing": "0cm", "Columns": 1}, "Layers": [{"Type": "layer", "Name": "default"}], "ReportParameters": [{"Type": "reportparameter", "Prompt": "ID", "DataType": "Integer", "Name": "P1"}], "ConsumeContainerWhitespace": true, "Body": {"Name": "Body", "Type": "section", "Height": "2.25cm", "ReportItems": [{"DataSetName": "数据集1", "Type": "table", "Top": "0cm", "Left": "0cm", "Width": "13.5cm", "Height": "2.25cm", "TableColumns": [{"Type": "tablecolumn", "Width": "3.375cm"}, {"Type": "tablecolumn", "Width": "3.375cm"}, {"Type": "tablecolumn", "Width": "3.375cm"}, {"Type": "tablecolumn", "Width": "3.375cm"}], "Header": {"Type": "tableheader", "TableRows": [{"Type": "tablerow", "Height": "0.75cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "post Id", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框1"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "id", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框2"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "name", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框3"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "email", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框10"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}], "RepeatOnNewPage": true}, "Footer": {"Type": "tablefooter", "TableRows": [{"Type": "tablerow", "Height": "0.75cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框7"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框8"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框9"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框12"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}]}, "Details": {"Type": "tabledetails", "TableRows": [{"Type": "tablerow", "Height": "0.75cm", "TableCells": [{"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "=Fields!postId.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框4"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "=Fields!id.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框5"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "=Fields!name.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框6"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}, {"Item": {"KeepTogether": true, "Type": "textbox", "Top": "0cm", "Left": "0cm", "Width": "3.375cm", "Height": "0.75cm", "CanGrow": true, "Value": "=Fields!email.Value", "Style": {"Border": {"Color": "<PERSON><PERSON><PERSON><PERSON>", "Style": "Solid"}, "TextAlign": "Center", "VerticalAlign": "Middle", "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Name": "文本框11"}, "ColSpan": 1, "RowSpan": 1, "AutoMergeMode": "Never"}]}], "Group": {"Name": ""}}, "Style": {"FontFamily": "微软雅黑"}, "Name": "表格1"}]}, "Name": "Report", "TransformationInfo": []}