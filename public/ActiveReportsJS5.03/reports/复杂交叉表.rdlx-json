{"Name": "复杂交叉表.rdlx", "Type": "report", "Body": {"Type": "section", "Name": "Body", "Height": "13.45cm", "ReportItems": [{"Type": "textbox", "CanGrow": true, "Value": "  补充资料：        \r\n                          1、××年度本级财政收入      万元；财政支出                 万元；\r\n                          2、财政用于教育事业费，教育基建费投入仅反映镇（街道）本级财政投入情况；\r\n                          3、××年年末实有在校生：\r\n                              ①普通中学              人；\r\n                              ②职业中学              人，其中：非户籍生               人；\r\n                              ③小学                    人，其中：非户籍生               人；\r\n                          4、××年执行教学公用经费定额：\r\n                              ①普通中学                    元/年生（执行在校生拨款、户籍学生拨款）；\r\n                              ②职业中学                    元/年生（执行在校生拨款、户籍学生拨款）；\r\n                              ③小学                          元/年生（执行在校生拨款、户籍学生拨款）。\r\n                    ", "Name": "TextBox270", "Top": "7.487708cm", "Left": "0.5cm", "Width": "41.5cm", "Height": "5.2026cm", "ZIndex": 1, "Style": {"FontFamily": "微软雅黑", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox270"}, {"Type": "textbox", "CanGrow": true, "Value": " 单位负责人：", "Name": "TextBox271", "Top": "12.7cm", "Left": "0.5cm", "Width": "2.5cm", "Height": "0.75cm", "ZIndex": 2, "Style": {"FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox271"}, {"Type": "textbox", "CanGrow": true, "Value": "填表人：", "Name": "TextBox272", "Top": "11.6946cm", "Left": "24.68563cm", "Width": "2.5cm", "Height": "0.75cm", "ZIndex": 2, "Style": {"FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox5"}, {"Type": "tablix", "RowHierarchy": {"LevelSizes": ["0.854856in", "1.816495in"], "Members": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "总合计", "Name": "TextBox225", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox225", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": -1, "BodyCount": 1, "Children": [{"Type": "tablixmember", "BodyIndex": 0, "BodyCount": 1}]}, {"Type": "tablixmember", "Group": {"Name": "Tablix1_学校类别1", "GroupExpressions": ["=Fields!学校类别.Value"], "PageBreakDisabled": "false"}, "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "=Fields!学校类别.Value", "ToggleImage": {"InitialState": "True"}, "Name": "TextBox5", "Style": {"BackgroundColor": "#f1f9f6", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "20pt", "PaddingRight": "20pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox9", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "小计", "Name": "TextBox261", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox261", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 1, "Children": [{"Type": "tablixmember", "BodyIndex": 1, "BodyCount": 1}]}, {"Type": "tablixmember", "Group": {"Name": "Tablix1_学校名称1", "GroupExpressions": ["=Fields!学校名称.Value"], "PageBreakDisabled": "false"}, "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "=Fields!学校名称.Value", "Name": "TextBox4", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox6", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 1, "Children": [{"Type": "tablixmember", "BodyIndex": 2, "BodyCount": 1}], "Visibility": {"ToggleItem": "TextBox5"}, "KeepWithGroup": "None", "RepeatOnNewPage": false}], "Visibility": {"ToggleItem": "TextBox4"}, "KeepWithGroup": "None", "RepeatOnNewPage": false}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "总合计", "Name": "TextBox226", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox10", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": -1, "BodyCount": 1, "Children": [{"Type": "tablixmember", "BodyIndex": 3, "BodyCount": 1}]}]}, "ColumnHierarchy": {"LevelSizes": ["0.279117in", "0.279117in", "0.279117in", "0.279117in", "0.279117in"], "Members": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "财政教育经费投入(万元)", "Name": "TextBox8", "Style": {"BackgroundColor": "#02a274", "FontFamily": "微软雅黑", "FontWeight": "Bold", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "White", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox8", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 9, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "总计", "Name": "TextBox59", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox59", "Width": "1in", "Height": "0.25in"}, "LevelCount": 4}, "BodyIndex": 0, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "教育事业费", "Name": "TextBox82", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox82", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 7, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "合计", "Name": "TextBox97", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox97", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "金额", "Name": "TextBox61", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox61", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 1, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "比上年增长(%)", "Name": "TextBox62", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox62", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 2, "BodyCount": 1}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "人员经费", "Name": "TextBox64", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox64", "Width": "1in", "Height": "0.25in"}, "LevelCount": 3}, "BodyIndex": 3, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "日常公用经费", "Name": "TextBox65", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox65", "Width": "1in", "Height": "0.25in"}, "LevelCount": 3}, "BodyIndex": 4, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "项目经费", "Name": "TextBox251", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox251", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 3, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "合计", "Name": "TextBox66", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox66", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 5, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "其中", "Name": "TextBox93", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox93", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "标准化建设", "Name": "TextBox67", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox67", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": 6, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "信息化建设", "Name": "TextBox68", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox68", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": 7, "BodyCount": 1}]}]}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "基建拨款", "Name": "TextBox70", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox70", "Width": "1in", "Height": "0.25in"}, "LevelCount": 4}, "BodyIndex": 8, "BodyCount": 1}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "其他投入", "Name": "TextBox78", "Style": {"BackgroundColor": "#02a274", "FontFamily": "微软雅黑", "FontWeight": "Bold", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "White", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox78", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 8, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "村投入", "Name": "TextBox254", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox254", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 5, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "合计", "Name": "TextBox38", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox38", "Width": "1in", "Height": "0.25in"}, "LevelCount": 3}, "BodyIndex": 9, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "其中", "Name": "TextBox255", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox255", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 4, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "人员经费", "Name": "TextBox85", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox85", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 10, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "日常公用经费", "Name": "TextBox92", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox92", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 11, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "项目经费", "Name": "TextBox249", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox249", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 12, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "基建投入", "Name": "TextBox257", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox257", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 13, "BodyCount": 1}]}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "社会捐款", "Name": "TextBox54", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox54", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 3, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "合计", "Name": "TextBox263", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox263", "Width": "1in", "Height": "0.25in"}, "LevelCount": 3}, "BodyIndex": 14, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "其中", "Name": "TextBox72", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox72", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "项目经费", "Name": "TextBox273", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox273", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 15, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "基建投入", "Name": "TextBox76", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox76", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 16, "BodyCount": 1}]}]}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "补充资料", "Name": "TextBox256", "Style": {"BackgroundColor": "#02a274", "FontFamily": "微软雅黑", "FontWeight": "Bold", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "White", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox256", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 5, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "信息化建设", "Name": "TextBox259", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox259", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 5, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "本年投入金额（万元）", "Name": "TextBox91", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox91", "Width": "1in", "Height": "0.25in"}, "LevelCount": 3}, "BodyIndex": 17, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "电脑数（台）", "Name": "TextBox79", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox79", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "合计", "Name": "TextBox246", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox246", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 18, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "Value": "本年购置数", "Name": "TextBox248", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox248", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 19, "BodyCount": 1}]}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "校园网数（个）", "Name": "TextBox260", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox260", "Width": "1in", "Height": "0.25in"}, "LevelCount": 1}, "BodyIndex": -1, "BodyCount": 2, "Children": [{"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "合计", "Name": "TextBox276", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox276", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 20, "BodyCount": 1}, {"Type": "tablixmember", "Header": {"Item": {"Type": "textbox", "CanGrow": true, "Value": "本年建成数", "Name": "TextBox262", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "#1b4931", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox262", "Width": "1in", "Height": "0.25in"}, "LevelCount": 2}, "BodyIndex": 21, "BodyCount": 1}]}]}]}]}, "Corner": [[{"RowSpan": 5, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "学校类别", "Name": "TextBox71", "Style": {"BackgroundColor": "#02a274", "FontFamily": "微软雅黑", "FontWeight": "Bold", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "White", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox71", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 5, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "学校名称", "Name": "TextBox40", "Style": {"BackgroundColor": "#02a274", "FontFamily": "微软雅黑", "FontWeight": "Bold", "TextAlign": "Center", "VerticalAlign": "Middle", "Color": "White", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox11", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}], [null, null], [null, null], [null, null], [null, null]], "Body": {"Columns": ["2.003323cm", "2.003323cm", "1.864346cm", "1.864346cm", "1.864346cm", "1.864346cm", "2.306949cm", "2.306949cm", "2.003323cm", "1.864346cm", "1.864346cm", "2.695128cm", "1.864346cm", "1.864346cm", "1.864346cm", "1.864346cm", "1.864346cm", "1.864346cm", "1.864346cm", "2.151948cm", "1.864346cm", "2.142976cm"], "Rows": [{"Height": "0.75cm", "Cells": [{"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value  Fields!教育标准化建设经费.Value  Fields!教育标准化建设经费.Value )", "Name": "TextBox227", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox227", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value )", "Name": "TextBox228", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox228", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum( Fields!教育日常公用经费.Value)/Sum(Fields!教育人员经费.Value)*100", "Name": "TextBox229", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox229", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value)", "Name": "TextBox230", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox230", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育日常公用经费.Value)", "Name": "TextBox231", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox231", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox232", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox232", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育标准化建设经费.Value)", "Name": "TextBox233", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox233", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox234", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox234", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanShrink": true, "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox235", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "ShrinkToFit": "True", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox235", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox236", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox236", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村人员经费.Value)", "Name": "TextBox237", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox237", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村日常公用经费.Value)", "Name": "TextBox238", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox238", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村项目经费.Value)", "Name": "TextBox239", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox239", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村基建投入.Value)", "Name": "TextBox240", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox240", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox241", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox241", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会项目经费.Value)", "Name": "TextBox242", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox242", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会基建投入.Value)", "Name": "TextBox243", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox243", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!信息化本年投入.Value)", "Name": "TextBox244", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox244", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑合计.Value)", "Name": "TextBox245", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox245", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑本年.Value)", "Name": "TextBox247", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox247", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网合计.Value)", "Name": "TextBox252", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox252", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网本年.Value)", "Name": "TextBox253", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox253", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}]}, {"Height": "0.75cm", "Cells": [{"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value  Fields!教育标准化建设经费.Value  Fields!教育标准化建设经费.Value )", "Name": "TextBox266", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox266", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value )", "Name": "TextBox267", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox267", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum( Fields!教育日常公用经费.Value)/Sum(Fields!教育人员经费.Value)*100", "Name": "TextBox268", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox268", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value)", "Name": "TextBox275", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox275", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育日常公用经费.Value)", "Name": "TextBox278", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox278", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox279", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox279", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育标准化建设经费.Value)", "Name": "TextBox280", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox280", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox281", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox281", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox282", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox282", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox283", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox283", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村人员经费.Value)", "Name": "TextBox284", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox284", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村日常公用经费.Value)", "Name": "TextBox285", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox285", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村项目经费.Value)", "Name": "TextBox286", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox286", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村基建投入.Value)", "Name": "TextBox287", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox287", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox288", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox288", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会项目经费.Value)", "Name": "TextBox289", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox289", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会基建投入.Value)", "Name": "TextBox290", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox290", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!信息化本年投入.Value)", "Name": "TextBox291", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox291", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑合计.Value)", "Name": "TextBox292", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox292", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑本年.Value)", "Name": "TextBox293", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox293", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网合计.Value)", "Name": "TextBox294", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox294", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网本年.Value)", "Name": "TextBox295", "Style": {"BackgroundColor": "#ddefe8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox295", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}]}, {"Height": "0.697214cm", "Cells": [{"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value  Fields!教育标准化建设经费.Value  Fields!教育标准化建设经费.Value )", "Name": "TextBox7", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox7", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value )", "Name": "TextBox58", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox58", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum( Fields!教育日常公用经费.Value)/Sum(Fields!教育人员经费.Value)*100", "Name": "TextBox53", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox53", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育人员经费.Value)", "Name": "TextBox51", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox51", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育日常公用经费.Value)", "Name": "TextBox49", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox49", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Name": "TextBox39", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox39", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育标准化建设经费.Value)", "Name": "TextBox34", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox34", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox26", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox26", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox21", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox21", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Name": "TextBox29", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox29", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!村人员经费.Value)", "Name": "TextBox86", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox86", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!村日常公用经费.Value)", "Name": "TextBox94", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox94", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!村项目经费.Value)", "Name": "TextBox250", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox250", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!村基建投入.Value)", "Name": "TextBox258", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox258", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Name": "TextBox264", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox264", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!社会项目经费.Value)", "Name": "TextBox274", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox274", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!社会基建投入.Value)", "Name": "TextBox77", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox77", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!信息化本年投入.Value)", "Name": "TextBox50", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox50", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!电脑合计.Value)", "Name": "TextBox90", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox90", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!电脑本年.Value)", "Name": "TextBox81", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox81", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!校园网合计.Value)", "Name": "TextBox277", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox277", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "CanGrow": true, "Value": "=Sum(Fields!校园网本年.Value)", "Name": "TextBox265", "Style": {"BackgroundColor": "White", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox265", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}]}, {"Height": "0.75cm", "Cells": [{"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value  Fields!教育标准化建设经费.Value  Fields!教育标准化建设经费.Value )", "Name": "TextBox320", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox320", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value+ Fields!教育日常公用经费.Value )", "Name": "TextBox321", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox321", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum( Fields!教育日常公用经费.Value)/Sum(Fields!教育人员经费.Value)*100", "Name": "TextBox322", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox322", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育人员经费.Value)", "Name": "TextBox323", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox323", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育日常公用经费.Value)", "Name": "TextBox324", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox324", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox325", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox325", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育标准化建设经费.Value)", "Name": "TextBox326", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox326", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox327", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox327", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!教育信息化建设经费.Value)", "Name": "TextBox328", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox328", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox329", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox329", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村人员经费.Value)", "Name": "TextBox330", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox330", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村日常公用经费.Value)", "Name": "TextBox331", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox331", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村项目经费.Value)", "Name": "TextBox332", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox332", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!村基建投入.Value)", "Name": "TextBox333", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox333", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Name": "TextBox334", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox334", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会项目经费.Value)", "Name": "TextBox335", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox335", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!社会基建投入.Value)", "Name": "TextBox336", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "#bfbfbf"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox336", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!信息化本年投入.Value)", "Name": "TextBox337", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox337", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑合计.Value)", "Name": "TextBox338", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox338", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!电脑本年.Value)", "Name": "TextBox339", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox339", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网合计.Value)", "Name": "TextBox340", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox340", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}, {"RowSpan": 1, "ColSpan": 1, "Item": {"Type": "textbox", "Value": "=Sum(Fields!校园网本年.Value)", "Name": "TextBox341", "Style": {"BackgroundColor": "#aedac8", "FontFamily": "微软雅黑", "Format": "n0", "TextAlign": "Right", "VerticalAlign": "Middle", "Color": "#404040", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}, "KeepTogether": true, "DataElementName": "TextBox341", "Width": "1in", "Height": "0.25in"}, "AutoMergeMode": "Never"}]}]}, "RepeatColumnHeaders": true, "RepeatRowHeaders": true, "FrozenRows": 5, "FrozenColumns": 2, "RepeatToFill": false, "DataSetName": "DataSet1", "Name": "Tablix1", "Top": "1cm", "Left": "0.5cm", "Width": "19.88188in", "Height": "2.555906in", "ZIndex": 3, "Style": {"Color": "<PERSON><PERSON><PERSON>", "Border": {"Style": "Solid", "Color": "<PERSON><PERSON><PERSON>"}, "StyleName": "=\"\""}}], "Style": {"BackgroundColor": "#f2f2f2", "StyleName": "=\"\""}}, "Page": {"PageWidth": "51.5cm", "PageHeight": "33.1cm", "PageOrientation": "Landscape", "ColumnSpacing": "0.5in", "BottomMargin": "0in", "LeftMargin": "0in", "RightMargin": "0in", "TopMargin": "0in", "Columns": 1}, "Width": "51cm", "DataSources": [{"Name": "DataSource1", "ConnectionProperties": {"ConnectString": "", "DataProvider": "TESTING", "IntegratedSecurity": false, "Prompt": ""}, "Transaction": false}], "DataSets": [{"Fields": [{"Name": "ID", "DataField": "ID"}, {"Name": "学校类别", "DataField": "学校类别"}, {"Name": "学校名称", "DataField": "学校名称"}, {"Name": "教育人员经费", "DataField": "教育人员经费"}, {"Name": "教育日常公用经费", "DataField": "教育日常公用经费"}, {"Name": "教育标准化建设经费", "DataField": "教育标准化建设经费"}, {"Name": "教育信息化建设经费", "DataField": "教育信息化建设经费"}, {"Name": "基建拨款", "DataField": "基建拨款"}, {"Name": "村人员经费", "DataField": "村人员经费"}, {"Name": "村日常公用经费", "DataField": "村日常公用经费"}, {"Name": "村项目经费", "DataField": "村项目经费"}, {"Name": "村基建投入", "DataField": "村基建投入"}, {"Name": "社会项目经费", "DataField": "社会项目经费"}, {"Name": "社会基建投入", "DataField": "社会基建投入"}, {"Name": "信息化本年投入", "DataField": "信息化本年投入"}, {"Name": "电脑合计", "DataField": "电脑合计"}, {"Name": "电脑本年", "DataField": "电脑本年"}, {"Name": "校园网合计", "DataField": "校园网合计"}, {"Name": "校园网本年", "DataField": "校园网本年"}], "Query": {"DataSourceName": "DataSource1", "CommandType": "Text", "CommandText": "ID(Int32),学校类别(String),学校名称(String),教育人员经费(Double),教育日常公用经费(Double),教育标准化建设经费(Double),教育信息化建设经费(Double),基建拨款(Double),村人员经费(Double),村日常公用经费(Double),村项目经费(Double),村基建投入(Double),社会项目经费(Double),社会基建投入(Double),信息化本年投入(Double),电脑合计(Int32),电脑本年(Int32),校园网合计(Int32),校园网本年(Int32)\r\n1,普通中学,西工大附中,641348,550785,686318,154826,990564,95276,84323,53163,36774,62420,70807,42586,2797,280,4238,424\r\n2,普通中学,高新第一中学,538706,9060,675882,266627,119572,11604,24824,91151,44341,71335,11149,44810,5111,512,4494,450\r\n3,普通中学,铁一中,353915,304467,248002,906017,799127,19671,37556,82640,38702,17091,65884,85672,1832,184,2193,220\r\n4,普通中学,交大附中,152209,201140,357825,296383,978999,74916,74996,13558,53022,66875,78172,32278,5931,594,6645,665\r\n5,普通中学,陕师大附中,76023,996447,609255,470540,305051,41818,64293,29822,39014,30010,42431,2764,4135,414,8622,863\r\n6,普通中学,爱知中学,495289,878213,167300,241927,670497,19209,6175,61288,45890,47092,90880,38499,8676,868,6112,612\r\n7,普通中学,陕西师范大学附属中学,521700,382870,734309,237789,248203,19554,24935,24789,66031,6725,47665,94370,5954,596,5327,533\r\n8,普通中学,黄河中学,85930,223859,135066,839517,207168,96231,85907,34460,94620,83394,98126,19312,2337,234,2682,269\r\n9,职业中学,师达职业学校,576575,798222,351020,920714,393236,88099,62782,53350,62880,26311,99393,45642,1070,107,3476,348\r\n10,职业中学,幼儿师范职业学校,140744,838921,316897,220623,475622,48009,71474,43893,60859,25368,98893,25167,3110,311,5856,586\r\n11,职业中学,明珠职业学校,702592,42279,670563,625918,508977,30718,7040,37359,12764,96893,28796,65120,1925,193,3985,399\r\n12,职业中学,华乐艺术职业学校,141866,255778,575815,796632,864093,95564,92350,27530,88407,16966,47344,56537,9970,997,4333,434\r\n13,职业中学,城市科技职业学校,971589,818828,826231,186993,887290,57269,67718,42550,64049,29011,55342,9230,4694,470,6847,685\r\n14,职业中学,培华学院附属职业中等专业学校,686999,156743,346756,754632,714424,98626,1917,33214,72670,88798,57354,14743,2265,227,4449,445\r\n15,职业中学,艺术职业高级中学,165527,337536,658534,255948,33914,50224,12545,10480,52163,18586,72884,29348,2671,268,8315,832\r\n17,小学,东六路小学,566058,355020,359514,471147,719093,43251,44183,85062,57787,7801,86171,75887,4912,492,4521,453\r\n18,小学,坤中巷小学,684040,564120,801686,228891,509394,63299,46233,64087,22235,73219,4522,54977,1745,175,9185,919\r\n19,小学,长缨路小学,378296,436289,666839,829967,128456,68911,83118,5946,80582,22607,82142,562,1190,119,6281,629\r\n20,小学,育英小学,625942,327388,748597,646353,826697,61687,43236,53826,35045,80680,89971,28481,7969,797,3444,345\r\n21,小学,西光实验小学,604797,53118,321233,200502,265446,39413,76850,62457,96326,30212,36829,71379,714,72,4807,481\r\n22,小学,华山实验小学,686396,340514,468240,989023,502573,55278,19026,19186,8879,92422,22877,37866,4853,486,1229,123\r\n", "Timeout": 0}, "CaseSensitivity": "Auto", "AccentSensitivity": "Auto", "KanatypeSensitivity": "Auto", "WidthSensitivity": "Auto", "Name": "DataSet1"}], "EmbeddedImages": [{"MIMEType": "image/png", "ImageData": "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", "Name": "Logo"}], "Layers": [{"Type": "layer", "Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}], "TransformationInfo": []}