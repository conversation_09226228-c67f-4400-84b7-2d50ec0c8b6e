{"Name": "Report", "Width": "21cm", "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "PaperOrientation", "Value": "Portrait"}], "Page": {"PageWidth": "21cm", "PageHeight": "29.7cm", "RightMargin": "2.5cm", "LeftMargin": "2.5cm", "TopMargin": "2.5cm", "BottomMargin": "2.5cm", "Columns": 1, "ColumnSpacing": "0cm"}, "DataSources": [{"Name": "订单信息", "ConnectionProperties": {"DataProvider": "JSONEMBED", "ConnectString": "jsondata=[\\n\\t{\\n\\t\\t\"订单编号\": \"D0001\",\\n\\t\\t\"客户编号\": \"K026\",\\n\\t\\t\"员工编号\": \"E004\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/1 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0002\",\\n\\t\\t\"客户编号\": \"K042\",\\n\\t\\t\"员工编号\": \"E002\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/1 0:00:00\",\\n\\t\\t\"运货商\": \"圆通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0003\",\\n\\t\\t\"客户编号\": \"K056\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/1 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0004\",\\n\\t\\t\"客户编号\": \"K076\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/2 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0005\",\\n\\t\\t\"客户编号\": \"K003\",\\n\\t\\t\"员工编号\": \"E003\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/2 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0006\",\\n\\t\\t\"客户编号\": \"K086\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/3 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0007\",\\n\\t\\t\"客户编号\": \"K064\",\\n\\t\\t\"员工编号\": \"E003\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/4 0:00:00\",\\n\\t\\t\"运货商\": \"圆通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0008\",\\n\\t\\t\"客户编号\": \"K063\",\\n\\t\\t\"员工编号\": \"E005\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/4 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0009\",\\n\\t\\t\"客户编号\": \"K056\",\\n\\t\\t\"员工编号\": \"E004\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/4 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0010\",\\n\\t\\t\"客户编号\": \"K062\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/4 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0011\",\\n\\t\\t\"客户编号\": \"K015\",\\n\\t\\t\"员工编号\": \"E004\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/5 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0012\",\\n\\t\\t\"客户编号\": \"K006\",\\n\\t\\t\"员工编号\": \"E005\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/5 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0013\",\\n\\t\\t\"客户编号\": \"K021\",\\n\\t\\t\"员工编号\": \"E005\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/6 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0014\",\\n\\t\\t\"客户编号\": \"K044\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/6 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0015\",\\n\\t\\t\"客户编号\": \"K012\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/7 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0016\",\\n\\t\\t\"客户编号\": \"K041\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/7 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0017\",\\n\\t\\t\"客户编号\": \"K075\",\\n\\t\\t\"员工编号\": \"E005\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/7 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0018\",\\n\\t\\t\"客户编号\": \"K029\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/8 0:00:00\",\\n\\t\\t\"运货商\": \"圆通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0019\",\\n\\t\\t\"客户编号\": \"K035\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/9 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0020\",\\n\\t\\t\"客户编号\": \"K002\",\\n\\t\\t\"员工编号\": \"E001\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/10 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0021\",\\n\\t\\t\"客户编号\": \"K047\",\\n\\t\\t\"员工编号\": \"E002\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/11 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0022\",\\n\\t\\t\"客户编号\": \"K058\",\\n\\t\\t\"员工编号\": \"E008\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/12 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0023\",\\n\\t\\t\"客户编号\": \"K055\",\\n\\t\\t\"员工编号\": \"E008\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/13 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0024\",\\n\\t\\t\"客户编号\": \"K068\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/14 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0025\",\\n\\t\\t\"客户编号\": \"K079\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/14 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0026\",\\n\\t\\t\"客户编号\": \"K018\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/14 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0027\",\\n\\t\\t\"客户编号\": \"K047\",\\n\\t\\t\"员工编号\": \"E003\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/15 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0028\",\\n\\t\\t\"客户编号\": \"K063\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/15 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0029\",\\n\\t\\t\"客户编号\": \"K023\",\\n\\t\\t\"员工编号\": \"E005\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/15 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0030\",\\n\\t\\t\"客户编号\": \"K047\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/16 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0031\",\\n\\t\\t\"客户编号\": \"K039\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/17 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0032\",\\n\\t\\t\"客户编号\": \"K076\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/18 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0033\",\\n\\t\\t\"客户编号\": \"K041\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/18 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0034\",\\n\\t\\t\"客户编号\": \"K022\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/19 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0035\",\\n\\t\\t\"客户编号\": \"K087\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/20 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0036\",\\n\\t\\t\"客户编号\": \"K019\",\\n\\t\\t\"员工编号\": \"E008\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/21 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0037\",\\n\\t\\t\"客户编号\": \"K037\",\\n\\t\\t\"员工编号\": \"E009\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/21 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0038\",\\n\\t\\t\"客户编号\": \"K059\",\\n\\t\\t\"员工编号\": \"E008\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/22 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0039\",\\n\\t\\t\"客户编号\": \"K044\",\\n\\t\\t\"员工编号\": \"E003\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/22 0:00:00\",\\n\\t\\t\"运货商\": \"圆通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0040\",\\n\\t\\t\"客户编号\": \"K073\",\\n\\t\\t\"员工编号\": \"E001\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/23 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0041\",\\n\\t\\t\"客户编号\": \"K003\",\\n\\t\\t\"员工编号\": \"E001\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/24 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0042\",\\n\\t\\t\"客户编号\": \"K051\",\\n\\t\\t\"员工编号\": \"E002\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/24 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0043\",\\n\\t\\t\"客户编号\": \"K054\",\\n\\t\\t\"员工编号\": \"E001\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/24 0:00:00\",\\n\\t\\t\"运货商\": \"圆通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0044\",\\n\\t\\t\"客户编号\": \"K090\",\\n\\t\\t\"员工编号\": \"E008\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/25 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0045\",\\n\\t\\t\"客户编号\": \"K016\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"货到付款\",\\n\\t\\t\"订购日期\": \"2017/1/26 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0046\",\\n\\t\\t\"客户编号\": \"K004\",\\n\\t\\t\"员工编号\": \"E002\",\\n\\t\\t\"支付方式\": \"支付宝\",\\n\\t\\t\"订购日期\": \"2017/1/27 0:00:00\",\\n\\t\\t\"运货商\": \"京东快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0047\",\\n\\t\\t\"客户编号\": \"K041\",\\n\\t\\t\"员工编号\": \"E001\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/28 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0048\",\\n\\t\\t\"客户编号\": \"K022\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/29 0:00:00\",\\n\\t\\t\"运货商\": \"顺丰快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0049\",\\n\\t\\t\"客户编号\": \"K072\",\\n\\t\\t\"员工编号\": \"E007\",\\n\\t\\t\"支付方式\": \"银行转账\",\\n\\t\\t\"订购日期\": \"2017/1/29 0:00:00\",\\n\\t\\t\"运货商\": \"中通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t},\\n\\t{\\n\\t\\t\"订单编号\": \"D0050\",\\n\\t\\t\"客户编号\": \"K089\",\\n\\t\\t\"员工编号\": \"E006\",\\n\\t\\t\"支付方式\": \"微信\",\\n\\t\\t\"订购日期\": \"2017/1/30 0:00:00\",\\n\\t\\t\"运货商\": \"申通快递\",\\n\\t\\t\"订购年月\": \"2017-01\"\\n\\t}\\n]"}}, {"Name": "订单明细", "ConnectionProperties": {"DataProvider": "JSONEMBED", "ConnectString": "jsondata=[\\n  {\\n    \"订单编号\": \"D0001\",\\n    \"商品编号\": \"P006\",\\n    \"订单单价\": 21.0,\\n    \"购买数量\": 12.0\\n  },\\n  {\\n    \"订单编号\": \"D0001\",\\n    \"商品编号\": \"P021\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0001\",\\n    \"商品编号\": \"P003\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 18.0\\n  },\\n  {\\n    \"订单编号\": \"D0001\",\\n    \"商品编号\": \"P059\",\\n    \"订单单价\": 54.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0002\",\\n    \"商品编号\": \"P041\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 10.0\\n  },\\n  {\\n    \"订单编号\": \"D0002\",\\n    \"商品编号\": \"P058\",\\n    \"订单单价\": 12.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0002\",\\n    \"商品编号\": \"P074\",\\n    \"订单单价\": 11.0,\\n    \"购买数量\": 24.0\\n  },\\n  {\\n    \"订单编号\": \"D0002\",\\n    \"商品编号\": \"P020\",\\n    \"订单单价\": 82.0,\\n    \"购买数量\": 26.0\\n  },\\n  {\\n    \"订单编号\": \"D0002\",\\n    \"商品编号\": \"P023\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 24.0\\n  },\\n  {\\n    \"订单编号\": \"D0003\",\\n    \"商品编号\": \"P019\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0004\",\\n    \"商品编号\": \"P063\",\\n    \"订单单价\": 37.0,\\n    \"购买数量\": 9.0\\n  },\\n  {\\n    \"订单编号\": \"D0005\",\\n    \"商品编号\": \"P030\",\\n    \"订单单价\": 27.0,\\n    \"购买数量\": 40.0\\n  },\\n  {\\n    \"订单编号\": \"D0006\",\\n    \"商品编号\": \"P051\",\\n    \"订单单价\": 48.0,\\n    \"购买数量\": 10.0\\n  },\\n  {\\n    \"订单编号\": \"D0007\",\\n    \"商品编号\": \"P075\",\\n    \"订单单价\": 8.0,\\n    \"购买数量\": 35.0\\n  },\\n  {\\n    \"订单编号\": \"D0007\",\\n    \"商品编号\": \"P067\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 18.0\\n  },\\n  {\\n    \"订单编号\": \"D0008\",\\n    \"商品编号\": \"P059\",\\n    \"订单单价\": 48.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0008\",\\n    \"商品编号\": \"P024\",\\n    \"订单单价\": 5.0,\\n    \"购买数量\": 24.0\\n  },\\n  {\\n    \"订单编号\": \"D0008\",\\n    \"商品编号\": \"P015\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0009\",\\n    \"商品编号\": \"P067\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0009\",\\n    \"商品编号\": \"P071\",\\n    \"订单单价\": 22.0,\\n    \"购买数量\": 8.0\\n  },\\n  {\\n    \"订单编号\": \"D0010\",\\n    \"商品编号\": \"P007\",\\n    \"订单单价\": 25.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0010\",\\n    \"商品编号\": \"P041\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 9.0\\n  },\\n  {\\n    \"订单编号\": \"D0011\",\\n    \"商品编号\": \"P030\",\\n    \"订单单价\": 24.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0012\",\\n    \"商品编号\": \"P026\",\\n    \"订单单价\": 25.0,\\n    \"购买数量\": 40.0\\n  },\\n  {\\n    \"订单编号\": \"D0013\",\\n    \"商品编号\": \"P028\",\\n    \"订单单价\": 37.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0013\",\\n    \"商品编号\": \"P043\",\\n    \"订单单价\": 47.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0014\",\\n    \"商品编号\": \"P075\",\\n    \"订单单价\": 7.0,\\n    \"购买数量\": 40.0\\n  },\\n  {\\n    \"订单编号\": \"D0014\",\\n    \"商品编号\": \"P037\",\\n    \"订单单价\": 27.0,\\n    \"购买数量\": 23.0\\n  },\\n  {\\n    \"订单编号\": \"D0015\",\\n    \"商品编号\": \"P061\",\\n    \"订单单价\": 23.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0015\",\\n    \"商品编号\": \"P036\",\\n    \"订单单价\": 20.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0016\",\\n    \"商品编号\": \"P063\",\\n    \"订单单价\": 42.0,\\n    \"购买数量\": 42.0\\n  },\\n  {\\n    \"订单编号\": \"D0016\",\\n    \"商品编号\": \"P055\",\\n    \"订单单价\": 21.0,\\n    \"购买数量\": 19.0\\n  },\\n  {\\n    \"订单编号\": \"D0016\",\\n    \"商品编号\": \"P058\",\\n    \"订单单价\": 14.0,\\n    \"购买数量\": 8.0\\n  },\\n  {\\n    \"订单编号\": \"D0017\",\\n    \"商品编号\": \"P024\",\\n    \"订单单价\": 5.0,\\n    \"购买数量\": 40.0\\n  },\\n  {\\n    \"订单编号\": \"D0018\",\\n    \"商品编号\": \"P006\",\\n    \"订单单价\": 24.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0019\",\\n    \"商品编号\": \"P042\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0019\",\\n    \"商品编号\": \"P044\",\\n    \"订单单价\": 17.0,\\n    \"购买数量\": 13.0\\n  },\\n  {\\n    \"订单编号\": \"D0019\",\\n    \"商品编号\": \"P070\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 9.0\\n  },\\n  {\\n    \"订单编号\": \"D0019\",\\n    \"商品编号\": \"P068\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0019\",\\n    \"商品编号\": \"P006\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 14.0\\n  },\\n  {\\n    \"订单编号\": \"D0020\",\\n    \"商品编号\": \"P030\",\\n    \"订单单价\": 23.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0021\",\\n    \"商品编号\": \"P025\",\\n    \"订单单价\": 14.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0022\",\\n    \"商品编号\": \"P059\",\\n    \"订单单价\": 56.0,\\n    \"购买数量\": 35.0\\n  },\\n  {\\n    \"订单编号\": \"D0022\",\\n    \"商品编号\": \"P036\",\\n    \"订单单价\": 20.0,\\n    \"购买数量\": 3.0\\n  },\\n  {\\n    \"订单编号\": \"D0023\",\\n    \"商品编号\": \"P053\",\\n    \"订单单价\": 33.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0023\",\\n    \"商品编号\": \"P064\",\\n    \"订单单价\": 34.0,\\n    \"购买数量\": 24.0\\n  },\\n  {\\n    \"订单编号\": \"D0023\",\\n    \"商品编号\": \"P053\",\\n    \"订单单价\": 33.0,\\n    \"购买数量\": 7.0\\n  },\\n  {\\n    \"订单编号\": \"D0023\",\\n    \"商品编号\": \"P038\",\\n    \"订单单价\": 264.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0023\",\\n    \"商品编号\": \"P012\",\\n    \"订单单价\": 39.0,\\n    \"购买数量\": 23.0\\n  },\\n  {\\n    \"订单编号\": \"D0024\",\\n    \"商品编号\": \"P055\",\\n    \"订单单价\": 22.0,\\n    \"购买数量\": 30.0\\n  },\\n  {\\n    \"订单编号\": \"D0025\",\\n    \"商品编号\": \"P041\",\\n    \"订单单价\": 9.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0025\",\\n    \"商品编号\": \"P019\",\\n    \"订单单价\": 9.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0025\",\\n    \"商品编号\": \"P063\",\\n    \"订单单价\": 44.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0026\",\\n    \"商品编号\": \"P035\",\\n    \"订单单价\": 17.0,\\n    \"购买数量\": 12.0\\n  },\\n  {\\n    \"订单编号\": \"D0026\",\\n    \"商品编号\": \"P050\",\\n    \"订单单价\": 17.0,\\n    \"购买数量\": 24.0\\n  },\\n  {\\n    \"订单编号\": \"D0026\",\\n    \"商品编号\": \"P030\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 14.0\\n  },\\n  {\\n    \"订单编号\": \"D0027\",\\n    \"商品编号\": \"P052\",\\n    \"订单单价\": 8.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0027\",\\n    \"商品编号\": \"P049\",\\n    \"订单单价\": 21.0,\\n    \"购买数量\": 18.0\\n  },\\n  {\\n    \"订单编号\": \"D0028\",\\n    \"商品编号\": \"P003\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0028\",\\n    \"商品编号\": \"P044\",\\n    \"订单单价\": 20.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0028\",\\n    \"商品编号\": \"P060\",\\n    \"订单单价\": 35.0,\\n    \"购买数量\": 23.0\\n  },\\n  {\\n    \"订单编号\": \"D0028\",\\n    \"商品编号\": \"P031\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 19.0\\n  },\\n  {\\n    \"订单编号\": \"D0029\",\\n    \"商品编号\": \"P027\",\\n    \"订单单价\": 41.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0030\",\\n    \"商品编号\": \"P064\",\\n    \"订单单价\": 34.0,\\n    \"购买数量\": 50.0\\n  },\\n  {\\n    \"订单编号\": \"D0030\",\\n    \"商品编号\": \"P050\",\\n    \"订单单价\": 17.0,\\n    \"购买数量\": 7.0\\n  },\\n  {\\n    \"订单编号\": \"D0031\",\\n    \"商品编号\": \"P007\",\\n    \"订单单价\": 29.0,\\n    \"购买数量\": 65.0\\n  },\\n  {\\n    \"订单编号\": \"D0031\",\\n    \"商品编号\": \"P044\",\\n    \"订单单价\": 20.0,\\n    \"购买数量\": 8.0\\n  },\\n  {\\n    \"订单编号\": \"D0032\",\\n    \"商品编号\": \"P027\",\\n    \"订单单价\": 39.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0033\",\\n    \"商品编号\": \"P045\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 10.0\\n  },\\n  {\\n    \"订单编号\": \"D0033\",\\n    \"商品编号\": \"P039\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0033\",\\n    \"商品编号\": \"P019\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 17.0\\n  },\\n  {\\n    \"订单编号\": \"D0034\",\\n    \"商品编号\": \"P020\",\\n    \"订单单价\": 68.0,\\n    \"购买数量\": 1.0\\n  },\\n  {\\n    \"订单编号\": \"D0034\",\\n    \"商品编号\": \"P029\",\\n    \"订单单价\": 124.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0035\",\\n    \"商品编号\": \"P032\",\\n    \"订单单价\": 32.0,\\n    \"购买数量\": 16.0\\n  },\\n  {\\n    \"订单编号\": \"D0035\",\\n    \"商品编号\": \"P059\",\\n    \"订单单价\": 47.0,\\n    \"购买数量\": 14.0\\n  },\\n  {\\n    \"订单编号\": \"D0036\",\\n    \"商品编号\": \"P047\",\\n    \"订单单价\": 10.0,\\n    \"购买数量\": 50.0\\n  },\\n  {\\n    \"订单编号\": \"D0036\",\\n    \"商品编号\": \"P049\",\\n    \"订单单价\": 21.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0036\",\\n    \"商品编号\": \"P028\",\\n    \"订单单价\": 46.0,\\n    \"购买数量\": 13.0\\n  },\\n  {\\n    \"订单编号\": \"D0037\",\\n    \"商品编号\": \"P021\",\\n    \"订单单价\": 11.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0037\",\\n    \"商品编号\": \"P006\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 19.0\\n  },\\n  {\\n    \"订单编号\": \"D0038\",\\n    \"商品编号\": \"P010\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 21.0\\n  },\\n  {\\n    \"订单编号\": \"D0038\",\\n    \"商品编号\": \"P053\",\\n    \"订单单价\": 33.0,\\n    \"购买数量\": 12.0\\n  },\\n  {\\n    \"订单编号\": \"D0038\",\\n    \"商品编号\": \"P029\",\\n    \"订单单价\": 113.0,\\n    \"购买数量\": 7.0\\n  },\\n  {\\n    \"订单编号\": \"D0039\",\\n    \"商品编号\": \"P031\",\\n    \"订单单价\": 13.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0040\",\\n    \"商品编号\": \"P020\",\\n    \"订单单价\": 67.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0040\",\\n    \"商品编号\": \"P073\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 7.0\\n  },\\n  {\\n    \"订单编号\": \"D0041\",\\n    \"商品编号\": \"P035\",\\n    \"订单单价\": 19.0,\\n    \"购买数量\": 12.0\\n  },\\n  {\\n    \"订单编号\": \"D0041\",\\n    \"商品编号\": \"P025\",\\n    \"订单单价\": 15.0,\\n    \"购买数量\": 4.0\\n  },\\n  {\\n    \"订单编号\": \"D0042\",\\n    \"商品编号\": \"P044\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0042\",\\n    \"商品编号\": \"P055\",\\n    \"订单单价\": 24.0,\\n    \"购买数量\": 20.0\\n  },\\n  {\\n    \"订单编号\": \"D0043\",\\n    \"商品编号\": \"P052\",\\n    \"订单单价\": 7.0,\\n    \"购买数量\": 2.0\\n  },\\n  {\\n    \"订单编号\": \"D0043\",\\n    \"商品编号\": \"P074\",\\n    \"订单单价\": 9.0,\\n    \"购买数量\": 19.0\\n  },\\n  {\\n    \"订单编号\": \"D0043\",\\n    \"商品编号\": \"P056\",\\n    \"订单单价\": 39.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0043\",\\n    \"商品编号\": \"P066\",\\n    \"订单单价\": 18.0,\\n    \"购买数量\": 15.0\\n  },\\n  {\\n    \"订单编号\": \"D0044\",\\n    \"商品编号\": \"P059\",\\n    \"订单单价\": 47.0,\\n    \"购买数量\": 60.0\\n  },\\n  {\\n    \"订单编号\": \"D0045\",\\n    \"商品编号\": \"P052\",\\n    \"订单单价\": 7.0,\\n    \"购买数量\": 28.0\\n  },\\n  {\\n    \"订单编号\": \"D0045\",\\n    \"商品编号\": \"P014\",\\n    \"订单单价\": 21.0,\\n    \"购买数量\": 9.0\\n  },\\n  {\\n    \"订单编号\": \"D0046\",\\n    \"商品编号\": \"P034\",\\n    \"订单单价\": 14.0,\\n    \"购买数量\": 60.0\\n  },\\n  {\\n    \"订单编号\": \"D0046\",\\n    \"商品编号\": \"P016\",\\n    \"订单单价\": 18.0,\\n    \"购买数量\": 9.0\\n  },\\n  {\\n    \"订单编号\": \"D0047\",\\n    \"商品编号\": \"P037\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 36.0\\n  },\\n  {\\n    \"订单编号\": \"D0047\",\\n    \"商品编号\": \"P066\",\\n    \"订单单价\": 15.0,\\n    \"购买数量\": 18.0\\n  },\\n  {\\n    \"订单编号\": \"D0047\",\\n    \"商品编号\": \"P002\",\\n    \"订单单价\": 20.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0047\",\\n    \"商品编号\": \"P029\",\\n    \"订单单价\": 124.0,\\n    \"购买数量\": 12.0\\n  },\\n  {\\n    \"订单编号\": \"D0048\",\\n    \"商品编号\": \"P034\",\\n    \"订单单价\": 15.0,\\n    \"购买数量\": 35.0\\n  },\\n  {\\n    \"订单编号\": \"D0048\",\\n    \"商品编号\": \"P076\",\\n    \"订单单价\": 16.0,\\n    \"购买数量\": 22.0\\n  },\\n  {\\n    \"订单编号\": \"D0048\",\\n    \"商品编号\": \"P064\",\\n    \"订单单价\": 34.0,\\n    \"购买数量\": 22.0\\n  },\\n  {\\n    \"订单编号\": \"D0049\",\\n    \"商品编号\": \"P056\",\\n    \"订单单价\": 37.0,\\n    \"购买数量\": 25.0\\n  },\\n  {\\n    \"订单编号\": \"D0049\",\\n    \"商品编号\": \"P048\",\\n    \"订单单价\": 11.0,\\n    \"购买数量\": 10.0\\n  },\\n  {\\n    \"订单编号\": \"D0050\",\\n    \"商品编号\": \"P077\",\\n    \"订单单价\": 12.0,\\n    \"购买数量\": 30.0\\n  },\\n  {\\n    \"订单编号\": \"D0050\",\\n    \"商品编号\": \"P035\",\\n    \"订单单价\": 17.0,\\n    \"购买数量\": 6.0\\n  },\\n  {\\n    \"订单编号\": \"D0050\",\\n    \"商品编号\": \"P024\",\\n    \"订单单价\": 5.0,\\n    \"购买数量\": 5.0\\n  },\\n  {\\n    \"订单编号\": \"D0050\",\\n    \"商品编号\": \"P030\",\\n    \"订单单价\": 26.0,\\n    \"购买数量\": 10.0\\n  }\\n]"}}], "FixedPage": {"Pages": [{"ReportItems": [{"Type": "table", "Name": "表格1", "DataSetName": "订单信息数据集", "RepeatBlankRows": "FillGroup", "Style": {"Border": {"Style": "Solid"}}, "TableColumns": [{"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}], "Header": {"TableRows": [{"Height": "1.5cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框13", "DataElementName": "TextBox1", "KeepTogether": true, "Value": "订单信息-明细", "Style": {"FontSize": "16pt", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "15cm", "Height": "1.5cm"}, "ColSpan": 6}, null, null, null, null, null]}], "RepeatOnNewPage": true}, "TableGroups": [{"Group": {"Name": "表格1_订单编号1", "GroupExpressions": ["=Fields!订单编号.Value"], "PageBreak": "Between"}, "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框7", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "订单编号", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框11", "DataElementName": "订单编号", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!订单编号.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框17", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框14", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "客户编号", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框9", "DataElementName": "客户编号", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!客户编号.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框12", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}, {"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框1", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "员工编号", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框2", "DataElementName": "员工编号", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!员工编号.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框18", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框15", "DataElementName": "支付方式", "CanGrow": true, "KeepTogether": true, "Value": "支付方式", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框10", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!支付方式.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框3", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}, {"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框19", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "订购日期", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框20", "DataElementName": "订购日期", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!订购日期.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框21", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框22", "DataElementName": "运货商", "CanGrow": true, "KeepTogether": true, "Value": "运货商", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框23", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!运货商.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框24", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}, {"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框25", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "订购年月", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框26", "DataElementName": "订购年月", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!订购年月.Value", "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框27", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框28", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框29", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框30", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}}], "Details": {"TableRows": [{"Height": "2.25cm", "TableCells": [{"Item": {"Type": "table", "Name": "表格2", "ZIndex": 1, "DataSetName": "订单明细数据集", "Filters": [{"FilterExpression": "=Fields!订单编号.Value", "Operator": "Equal", "FilterValues": ["=Fields!订单编号.Value"]}], "RepeatBlankRows": "FillPage", "TableColumns": [{"Width": "3.75cm"}, {"Width": "3.75cm"}, {"Width": "3.75cm"}, {"Width": "3.75cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框31", "DataElementName": "TextBox2", "CanGrow": true, "KeepTogether": true, "Value": "订单编号", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框32", "DataElementName": "TextBox3", "CanGrow": true, "KeepTogether": true, "Value": "商品编号", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框33", "DataElementName": "TextBox4", "CanGrow": true, "KeepTogether": true, "Value": "订单单价", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框40", "DataElementName": "TextBox1", "KeepTogether": true, "Value": "购买数量", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框34", "DataElementName": "订单编号", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!订单编号.Value", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框35", "DataElementName": "商品编号", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!商品编号.Value", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框36", "DataElementName": "订单单价", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!订单单价.Value", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框41", "DataElementName": "购买数量", "KeepTogether": true, "Value": "=Fields!购买数量.Value", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}]}], "Group": {"PageBreakDisabled": "false"}}, "Footer": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框4", "DataElementName": "TextBox1", "KeepTogether": true, "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框5", "DataElementName": "TextBox1", "KeepTogether": true, "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框6", "DataElementName": "TextBox1", "KeepTogether": true, "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框8", "DataElementName": "TextBox1", "KeepTogether": true, "Value": "=Sum(Fields!购买数量.Value)", "Style": {"Border": {"Style": "Dashed"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Left": "0cm", "Top": "0cm", "Width": "3.75cm", "Height": "0.75cm"}}]}]}, "Left": "0cm", "Top": "0cm", "Width": "15cm", "Height": "2.25cm"}, "ColSpan": 6}, null, null, null, null, null]}], "Group": {"PageBreakDisabled": "false"}}, "Left": "0cm", "Top": "0cm", "Width": "15cm", "Height": "6.75cm", "FixedWidth": "16cm", "FixedHeight": "17cm"}]}]}, "DataSets": [{"Name": "订单信息数据集", "Fields": [{"Name": "订单编号", "DataField": "订单编号"}, {"Name": "客户编号", "DataField": "客户编号"}, {"Name": "员工编号", "DataField": "员工编号"}, {"Name": "支付方式", "DataField": "支付方式"}, {"Name": "订购日期", "DataField": "订购日期"}, {"Name": "运货商", "DataField": "运货商"}, {"Name": "订购年月", "DataField": "订购年月"}], "Query": {"DataSourceName": "订单信息", "CommandText": "jpath=$.*"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}, {"Name": "订单明细数据集", "Fields": [{"Name": "订单编号", "DataField": "订单编号"}, {"Name": "商品编号", "DataField": "商品编号"}, {"Name": "订单单价", "DataField": "订单单价"}, {"Name": "购买数量", "DataField": "购买数量"}], "Query": {"DataSourceName": "订单明细", "CommandText": "jpath=$.*"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}