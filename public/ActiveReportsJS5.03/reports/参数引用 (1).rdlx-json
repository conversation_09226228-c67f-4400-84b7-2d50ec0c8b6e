{"Name": "Report", "Width": "10cm", "ReportParameters": [{"DataType": "String", "Name": "userId", "Prompt": "userId", "ValidValues": {"DataSetReference": {"DataSetName": "DataSet", "ValueField": "id", "LabelField": "id"}}}], "Layers": [{"Name": "default"}], "CustomProperties": [{"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}, {"Name": "PaperOrientation", "Value": "Portrait"}], "Page": {"PageWidth": "21cm", "PageHeight": "29.7cm", "RightMargin": "2.5cm", "LeftMargin": "2.5cm", "TopMargin": "2.5cm", "BottomMargin": "2.5cm", "Columns": 1, "ColumnSpacing": "0cm"}, "DataSources": [{"Name": "DataSource", "ConnectionProperties": {"DataProvider": "JSON", "ConnectString": "endpoint=https://jsonplaceholder.typicode.com/"}}], "Body": {"Height": "1.5cm", "ReportItems": [{"Type": "table", "Name": "表格1", "CustomProperties": [{"Name": "ReportStyleId", "Value": "6bf0b2cc-95b7-437e-b26b-8b07d2f4b0cc"}, {"Name": "ReportStyleName", "Value": "主题色1-交替行颜色"}], "DataSetName": "DataSet1", "TableColumns": [{"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}, {"Width": "2.5cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框1", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "userId", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框2", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "id", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框3", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "title", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框4", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "completed", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}], "RepeatOnNewPage": true}, "Details": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "文本框5", "DataElementName": "userId", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!userId.Value", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框6", "DataElementName": "id", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!id.Value", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框7", "DataElementName": "title", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!title.Value", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}, {"Item": {"Type": "textbox", "Name": "文本框8", "DataElementName": "completed", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!completed.Value", "Style": {"Border": {"Style": "Solid"}, "TopBorder": {"Width": "1pt"}, "BottomBorder": {"Width": "1pt"}, "LeftBorder": {"Width": "1pt"}, "RightBorder": {"Width": "1pt"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "White"}, "Left": "0cm", "Top": "0cm", "Width": "2.5cm", "Height": "0.75cm"}}]}]}, "Left": "0cm", "Top": "0cm", "Width": "10cm", "Height": "1.5cm"}]}, "DataSets": [{"Name": "DataSet", "Fields": [{"Name": "id", "DataField": "id"}], "Query": {"DataSourceName": "DataSource", "CommandText": "uri=/users;jpath=$.[*]"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}, {"Name": "DataSet1", "Fields": [{"Name": "userId", "DataField": "userId"}, {"Name": "id", "DataField": "id"}, {"Name": "title", "DataField": "title"}, {"Name": "completed", "DataField": "completed"}], "Query": {"DataSourceName": "DataSource", "CommandText": "=\"uri=/todos?userId=\" & Parameters!userId.Value & \";jpath=$.[*]\""}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}