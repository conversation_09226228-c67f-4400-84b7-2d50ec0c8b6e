{"Name": "不固定列.rdlx", "Width": "31.204cm", "DocumentMap": {"Source": "All"}, "EmbeddedImages": [{"Name": "Logo", "ImageData": "iVBORw0KGgoAAAANSUhEUgAAAoYAAAB0CAYAAAAYT/NCAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAMyxJREFUeNrsnQfYHFXVx8+bBoRAIBB6LwGUgPQiSACD9N5DQkCaUhQVRLoUsaCAgIC00CJFOqJSYgDpkSK9hRZqQEikhyTf/L45a5bJnd0pd3Zn9j3/57kPZPbdO7N3bvmf3jV9+nQxGAwGg8FgMBh62BAYDAaDwWAwGIwYGgwGg8FgMBiMGBoMBoPBYDAYjBgaDAaDwWAwGIwYGgwGg8FgMBiMGBoMBoPBYDAYjBgaDAaDwWAwGIwYGgwGg8FgMBiMGBoMBoPBYDAYjBgaDAaDwWAwGIwYGgwGg8FgMBiMGBoMBoPBYDAYjBgaDAaDwWAwGIwYGgwGg8FgMBi8oJcNgcFgMBgMHYmBQdsmaOsFbaWgLRa0AUHrqvubT4P2btCeDtpjQbs1aPcGbboNX/dE1/Tp9u4NBoPBYOggzBu0k4K2d9B6Z/j+M0H7WdButKE0YmgwGAwGg6G6WEVCrd8CHvo6O2g/CNpUG1YjhgaDwWAwGKqFRYP2iIQaQ1/4RdCO8tDPakGbI+HfQkSfCtp/7JUaMTQYDAaDwZANfw7aDjGf4Us4LmivB+39oE0L2uxBmz9oKwZtyZjv8XdoIf+d47nmC9pbki7glWdcKmiT7bW2FhZ8YjAYDAZDvnMUcjVA/7+/Xkfr9d+gTZIwuOPjgp9j8aBtH0MI8Rc8P2ifNPj+qkE7M2jrRq5D5g4O2r45nm1rSZ8FZR4lq4/bFDNiaDBUHf2CtlbQVlCJl81tbgnNKLMGbYr+3STdtN/W9qaETt9P6L8NBkN5QBAHkb1rBG11Xd9E+S6UkPSgqXs+aE8G7Z9BGxu09zw+35by1WjjGg4K2kUJvo8JehN9viUin22V89l2yvi9KTbtWg8zJRsMHtaREkE2v4308MibI3SiHh63aRtvw2wwtBzzKOHaVklTX499Y6K9P2h/CtqVEppO8+C8oO0XufaBhClr0gSPHB60XzmuE8zyTobnQpP5cgxpbQaE6ldsGrYWpjE0GLJjEZXGh6vWwCfYzLfTBvDvuUwlf3PINhiKBSTw+0oKexZ0D4THb2o7NWhXBO2XQXsxY3/zOa49Jukjih+JuT5/RmK4r4MUfhG0I+ueDW3sr23alQOmMTQY0uNrQTsmaDu2QbjC9PxHlejfsldhMHgFJtMTgvaNNt1/mhJEfALfSPndW4K2ReQaeQi3TdkPpvKHHNdXa0Aa4zCXhNaOuSPX0ZLuHvm7D2LI7kSblq2FaQwNhnQS+c9VAu7ZpmeYTcK8YpiMTlEp+3N7NQZDLiwXtHOCtmGK76BVmSChmZT2jgpuk/SzWSQMRBmgwuRKMiMwJQ5oEbFAEFlMihiCQZJq/FwEauEMY7FgzPUs/pA/dJBCUeE2CczHsA0wjaHBkAwjg3Z6go291SDX1x4SmowMBkPKM1DJyylK5JqBUnFj9L/4B6ZNpUIw2ubaNkpwT+6zm4SBK81wSNDOiFxDA0mATBrtI+4qezlI4cCUvxVXG8rsRXMXonVczSF0u8zUkMoPbZoaMWwlkOSWkNDBdWE99NHI4GDcR0I/CCSW/2h7TdsL+pmh88HGdIG400CUBWgMDwjaKHtdBkNizBm0SyWsJdwI7PekerlK939fmFcJ2AFKGOOAiZXAtjub9MdZNl5m9uf7m/7GJGfWxkH7u8xsEYEsfjfl77te3GZstKHXOZ79ZcffokE17ZURw8KA1EIh8bW04UcxT8a+UO3jIEyy0AckTDvwpE2njsPyQbspaMtW5HlPDNqx9toMhqbAXErZuEa+hAR8oUm8WkLNW1GomY9Zv4s2OHMOUCG1ESCB33FcfzRox+nnLvPsUtr/oeJ2MePMfCjFb9pL3ClyHgzaOg6yF0cMu2yqGjH0jYVUUiK5Jmr7PgXe6y0lETcE7Xax2pJVx9pB+4uEWuUqgaCUI+z1GQyxwDp0V9CWjvkcH8HDgnZhwYQwCnKckirmGIn3/z9C3KlkahisCou4sw7rQq3U3GcSWsmWksa+iJcrcU0KqqTcp78nCkjhA47rX5eZlSuMfU+brkYMfUlfmwbtexL6cfRowzOQnPiSoJ0rloOpilhLyf0cFX1+Ntmn7TUaDDMBInS3hIEgLtwjYbTshDY+I1pMIpO/FvM5Ecu/bPD9PcWfWwm+yxtIcl/KZZR0u9J3nSVhBZW43/yog6DPZVO2PSSqU4CEhCocHws0PVu28feRCPSnQXspaNeqBGWoBlaWMKH0HBVfCwaDYebz7k8NSCEJojdqMymskTFcna6O+RzzdiN/P5QSu0hYji8PMDtvmIIULq3fcZHC5/VMNFQAnaAxRNU8UkL/iUVL/Jw44h6hC8RQTuB3NE78J6tuNVaRzotS7q3re0EVvAZq66dtdgkDx+qjPPGl+rjuvzjfk9KD6EfKD74iYTBBOwPJMLdRoxYtL0FwRGfOrfsapj60JkSE/lvf6XMlfT+YPldQckDDNDmX/r5atRAIxjT9PbjeECmLrzamzU9b8IxHS+jH50IZXTDwr/uthH5/UTCOawbtXw2+TzQy6bV2k2QR1zVg0j1JiWlSgoCVhTyK8zo+g6DimtPIijEkaP+IXHtH17rBiGEqrKpS3uoVed4vg/Y7XayftEA6ZrESAba1btTH6P0N7oNtrIRVCKqOKhPDvvr8mJaWV8K0jM7fIiwAU+vICc719+t/PyvwN0L8dtW1uV7KQ/tlFTIvlPa7C/BeiJbdQH/H7Bn7maYCM2lgiIi9U4m8T6yu79blu3d6DPkqC06TMKVOFAR4jErwfcznW+o7Wl4JYz+ddwgeRD2P13l/exOy6SKvPwnaySq8RYFQRmTyrU36cRHD1/VZDUYME0vZv5Awb1MVnVPZ3EdIWAvXJ3rqAiO1ynYyc6LSKSqBWUm1mYGEfFSH/JakxJADIa8PD33kCdCZV+csbX0lgu1e05+qkHCTak18rRfWHlopkpPPlrMvNm5SfqAFe7bFAtSwoO0vYSBBEUDDRO3gP3gScHoq6VnV8dktKjiX+SCEfGGi3SQjMSxyn8FvcN0GhJ+gldEJ+hoqoQtPFJMLfDcIhfibjlSCnBf9VVib3eMz4o5GNZu3jRg2BhLPNUFbseKHN4sGs8YJki/ybRbdMLZVMjh3k7/fR7UNhhkg2eqD0jkRcEmJ4YeSP2F3FgdxTKY764GMianMvs5o+W/QA/CuHP1ApH4j/n1XiTLFAvFrKTYTQpcKs1gdlm7h+EPMj9QDMivY8853XEcjtbK4S7GVDcdL6C5VBmI4WN/Jzg3W7qdKCq9N2Cfn1/VtGlvM9T/x0M/eBZ2tuAw83MoBqVrwyTYq+VWdFNbG/jiVWOdM+V3MADupVD1RNRt7JyCFot8zfPU9nCeWFqFoYCLGYZ40FpitfqmahrLvQWjIqIk9VrUL38rwuyE350oxAU0IhlhPbpbiAqaWVlI8qsWkUJR8PK7EOgtmVeHbhe9WhBSmAWuKqOrZPPeL8DdS1wH+rrs2WLv4726cghS2G8M87f+7F/Bsk3S8W77pVQWEuZ8unRVJDTbTw5IUO42i4TDXbamH1FBx54hKgo21LzMnh0ALspoNQ2HAfIp/FBkD+lf8t6yvBIm8bvikNasdCym8XeJNbb73EVKtbCB+zGI1bK2/t51R+rMrscbv9CBJpxmFzLhq/16v78bnWYr2Gw0kdZcX0zFDiEer+5GELkRP6H5f1GE/KGgXS6ix4/fdqvPiuZTjNr+Epvc19cxYNyF5ulmVFGnrKvdt8x7Fb7wtRx8ELG5UwLNdo/PHiKED+OWc0sGH59dVEiNj/UuRCYuKHZ/BDT29r15KMC81zvL/aV1OaNO9P9HN82N9jjmVOHVKqhk0DEeqQDdrh82bPXQ9ojWJ8xPmEL2hRaSwBkgJqbo2ET/BbQcG7UwpT/UJhAs0YZhQk/hAdYk7qARf6x97eJ4uJQP7696dxvLzuhJurBWvFjBWsymp31r/DVF8Vu9FRDjBVZOVuPbS9TpQz5xlJL0VC6XGj5TIZAGE+ectmEOMy2GOOb17TmK4m6PPiSrQJHEVY8yHOa5f3o6FVgVi+JMOJ4U1LK2SZK2YO+TtmwVtyjsZMfx/kAi2VSmOeLdEXGKOfEbcBeMBZRrxoyWP2RA9eKqWUxEt7O8ke8nJKoAoaaJo8aO6yvE5UZpD2/Bc39TDaETOfigQcFZJ1+wLOr7NAHkf5Lh+mbjLr6UBGiaihQdn/D77DomqD9e9GN/NNwomRKuI/5y6BEXgo3eO5Iskf0JbK7Cavr96bKdzPmvaJBepo3xh0hKlR8YID/e0Y5GV3SyLZP6bbkRU5tPNBv+r9QqU1NEo9BfDIQX3jwngDAlrLXNgo50c24AUgveDdq+EbhNoi+dXIj+mAuNJhDE+s5d0OCmsgfQcRFzu6iBnh7fxuYbHHFRJsVVJSWENaJZWTzgOLuRJ2YXJE1PtHTlIYT3QLKMBxdT7falObeB79bkpp3eq+E8vVCRcShE0pFtm7G+FGMKdRtu3h+Ma1W+mtWOAykwM8W1oZfQspgmS3WIawk/iRgl9NCjTM1k6C5grt+7mpJAggiKDmDDpofnDv+7FHP0gwf5ZJdxVxa9flE/g//WIhKkVuhN66EGzXt2/zyrBAX9mRnK+iP6eMp8NPRMQV6xh2zmuQ+ieynhfTK1o/kcW8JvwozxbwhREZbQQfKrC6aFKBpnvo6Q1icl9g6CYjxzXswaPuIQwskIkzTO6ipLLPMTSK8pqSh6gh2HR/lbP6X1IqEq6kkZ+OSyG9ZVQUYO56n5TBLFc1o2J4YiC+kXCw/3hdPGffwshBW0vPqfkeJu/JGNJIvXbJL1fUh58ousXv6mJKrxxbRYlDmgvCTpYXMKas0XuJWgOyRCwkr6fb5TgnZChgNyc38tAcqtQn5Y5R8De32I+J8eiyypyZQ5SOCahMPm+zk3++6HOxwX18G9WyQMrAebDoTqvfYCo7gckDI5ZXhonVCdwCYvGK7q2aON07/myQ/b+j/XcjxL8zXXdpIlU74ohlFek6GN4DLF8ql0DVFZiiORUpO8X2kDU32kSTI/XdoluOPtKqA1auKKLgwOsX4zk1OngIN+hgH6n6iZxdcHPj1bhbiX2m7Z5LJlDt7dAy/GBks+xekC/kIJ491LSxsa/k8TXys0D9oFfSLnqorNH4bP8WgphacMKreODGhBDl38npQ+z5Mpj/jTLnQuBwsJ1izROOL6cCuXkVlwi5m9W1jnOu3jPwzhB8g6ICA39de1+osLsJ0oKP5fugUscxLCPvpvzU/SDALJk5Br70uiE30do2C0nsfSOMpoLsPPvWlDflPpZV6WyPFVHJimxxHfsOGlvrdWsmFWy+1RUHd8qSCtyYAtIYQ0cGFvoPGwnehZICj/TDRKft/l1XyCw4nlJp41F04GZ+yQ9dDdQDYpvEJ26ZonmOe/msIR/S3DCiQU8A2mx3iiIcCAUDWzwWRT3SLY0XcfqnHGB37aLhFrpU6V5FRo0iScrQTxI4rVTK6oCowhN9wdKFp9UZQf//243IoXgLv3dUaQ1J7vMyJD6NxN+HxehqBZ5Wgpi2S2IIYvgzIL6ZtNDlX6/xz7xryCggCinpyq4OLprsusitGw4pJ/X4t8xTQ/+wzvs/bCpUuoNq8EeqoWZ4rH/u1VA/Im0ybm7hSBAoF+CvyPwYRFP9yTl1n5K5ufRfhFEiRD+lcd3CfEdGnOOuDS3YzPcAwL305jP0GCvpMJgWrcRlAlYxkhVdkfM36xbAsGvU8H7cgWhIAAktQL2jjlD02j78hLLbkEM8YdZwnOfkLftVeorykcCyQuV8l8rtjg2T3hodBp8m8vQGvywjb+HyP0jOuC94JNFfjn8edGqvFfgvTgYfqsagk4mhwQ17JjgHDjI0/1wb0DbhTnu3chnL+g83VT8WVlcmjy0d708EcOfi1trR+JoNNl5CwW8peNxdsznB0u1zPtVgosY4jO4W8Lvo+2LaqyxclyX8PtEuLtcmi5v98CUycewTwGaD0jhltKaVB8UfsdE/WfdMKoApHgSs17bjTYDTGa+gwMQOtoduY4mBg3bgRWV3n8vYT3YD1t8b3IQLietSa7bLkB+RzX4fCtPAjmlOdFQNquwMUaFmaM83HNlxzXX+ob8j0vZN65COzuuYy7e1SO5nSozKrq4UmidpmPbChD4eYH4URpNSCFwoDnzZcHCenNjgr9Ds417wfqO9XJqwnUVBRlNklYe2kYFt6zEslsQQ/w0FvLc517S2vxvX+hGgv9CmXyNGmGVbkYMOUh81kV+RUJH5jIAreUKFXsf+FyNlGJ8/pLiF3rQt3LsSHJOtCgaJ6JEKaGG1aEIDf4GegDF5Zob7uEeaHu/K8nLrhG1f5jk96FbPiFZHK+Hbhrwe7pirv+3oPU7r4Nw8HvmbtG8JLPAdp76ejUFMVxDiZIPzJeQGIJLHcSwlj7mmSYKBtfzptH2udbdDQXNrVQokyn5AM/9/V7cFQmKBpvPDuK3XmmRz3qTdC/4zl14tqSrQVokcJXAbPhyRd4FAsnqbSaFtXE7uQX3wbcOH2rKX2HuxGSFhpcIVbIE4JNHJPG7nu8L+dq4wQHnw+cWs3wa0z9/68Pfu7/+hnos7vi7LD7gw2Lm7H0FzY/pSjofd3y2WAX32iltui8C1rIJ/xb/0E8Tvvt6bC4zp+dCyEvqTgZ5HZqTWHY8MWTS+6wpior4Z238PajQDy3pYmXz+ac+H2X4HupmxHAFz2M5umS/74OKCCVE82M6Kku6pGsLHrc3dI87RL5aD70epAzBjIeG6F+e779ezHUI6ew5+8ZScn6G7/kiWPNF/u0KHng2ZZ8ElSwSQ4CLFtZ3lM5II5amGkpvz/dOmqcWF6AbHNddtY/rsXsMyUxKhrGQRi225K28rQwvrizEcFvP/R0lforI58EoCZNUlgFoRKjTS3APofGozjHlvCndDz4l7we66RjmAb5eRKyeIP4TgDcCWjPMjlvo/dkjBkYO5DsKujcJg4dIch836s9uJqEpzhfWirk+xEPfFAjIEij0iqffNmsCYph2nW4U87z3t2CuUinp6A5Z60kxu+d7D5fk1YdGOa4t1WDNoKXe3HE9bwm8NMSyUJTFx3ATj309I63LJdcIHHonSvv89zjoSP6KIyvpPj4QA/CZOH2MDWfqgwJJvujkrfjpoZ0jPRWppHAfWMIhCKPZr/cPJY1NEYnPd5f0ZRHRHuzrUYMQl3j7mx76zpqN4Q1Pv222iLJjgRhynpdI39LCtXKWrpVVS7BumYs7N/kbAph+FLmWNyCP9F9JKtVQVSZqucGdAN/asQm+f4fOxahAMUzcbi7bOIQRhIakGnDM3K4YhNJUIisLMVzHY1/nt1gT0Qg364bUqtJlLETSKBAZjYawO1Y1aYaBHvu6z4YzFb5fICnkACUDAZo2HNmTBBitHyGG/y7gua7IIUBQUQZt3MYenmMOJUxvRwiVj0ot92b83seexrg+qGTOmHf/Vso+V2zzesdv+TB9/+3GJwkI1hIF3HecJE8x9AMHmR+R8PsIrGj7ovkqMfficvWlQ9CLYnQK3uHSFiI4PliWjboMpmQm1ACP/ZUpwha18A0F34NowIv0UMTXZjeJLxJu8Bvd96QNZ2KcKP4TgC+j/eKzh08eKWfWluRR54Mi/x7v+fk4KI7P2cfZHp9nOce/8yoHpuZYB77yR36cQNkxOcPciuLfLV4zCBSN3BvKtMe7ounT+Oz2dVxLk3f4Qse1HWP6dWFUjBLh25Fr8zmuiSQ3I2PeHpbj+92GGC7rsS8SqL4m5UIRfktIv2fpBEUbSSTbX6R7lTTKCl+l8D4r4VwrK9BgH+exP6Jo/6brHV+spTztPW95/t1jJL0JOYpbPRKAhZoQxSwgAj5rPr++nn7Xe00ISlqSQcqYWWN+a6sRF0QJ6fpnidZ4rxjBKCnypi0iA0k0rgAtedLUOwQnuQIxhznIZlTwfFQap7apB4Lr0o7rV0jJX2ar4TMY4PESHoqPF7BR/Fo6v5RXUejy1M/bJSWs9ZtyGaKTIUZ7ix/3DkyqpJVZy9OzRaNZpyiB8LUv+rAWIOwR8DDUQ19RN4rlPRHDrJjXw/1JEVKfbiQuuvWjnM81WdoT0Ig5dY0I4WWOPi35q64UjTT5+FzvLY2rwWQVQKPRyHumIF24lUR9/yCWs9XNMVdVlLxBJw94ECA7jhjO67GvF0q4OHybp043UlgK+NyUkUCJYF+8pCQ4K6bpRpo3YSvCIxpy3xWFsJhQy/f9yGHU31P/vqwFD3sihlGXHR8FBfJE5fsIrHgu8m8f0a0ubWE7haxxUk2kye/qem9pI3QvchBDhEmCSpIEOv1JwiozfSLPRbDJlboPrefY465M+HyQ311yEsuWoAym5Nk89jW5pIvD53NNEUPVpOEkc+SsDhyj0zwcaqRYelqKKzM5R+Tfn3nql0jOZz315YsYRBPy+giKez/Hd4d4uP+jCc+ANEqQfjFCjiH53GrHmJFV4CUHxxmW8Ptk7rjZcb0WbLKr47MxKYSj76ggWg+0v1eV7WX26LDJOb0bPFeXGPKgrMmfr+6wccZf7/gc30drh2noD+I/x1krBK9nPD7ThIL2ex/EMKvQu4oUExE9LQXZi4MF7vnhEmnmhsuVJm3UOufsKMf1PVP04fo+mQ4GxBDDNL6BrhJ4ZA95rwovs8rPMEcJF0yX+DNN1SQMQ/tJ+pyen4tAlk6qQnN8jgN2SQn9bnZow3P72kNe8vhMr5f4t2YVtHxUpoIE3paQjMySol9XMM1sYmg1sghplzr2eMpPrp7w+wS1RUtSom0+1iHIYF24LsV5sbXj+mVlHPh2E0MGa3OP/S1dwjH26Tc20faK3PAlnfUt4Nn+0SFj/HqM5J0EgyXM57V8Qc+G2f55CU1GpIIpyoF/gse+3i3oGfu1aX7spM3Heomu57gAkTT+lK45QaBSHzEURex8WQVei9lHk5bIQ/Hi0gL+wHHtRkmuFd1eZvZdxR3pJiOGMySvbZQpc4Cs5rHvwSUc45U89mXl18pzyBZBDB/okDH+nWRLYfJ1CX12Bnp+HojmMUH7lpKh5VR6PyiysRME5Eu779Ov2Jev1oclmBv4WV3iqa8/Oq6hxXFVeVowRb9vx8zfJW37jIVr3eStlZw1Abprfu0myesxJxVq80Yjk2/40+5MDJk02OevkVDrdYMOlG9z3NdTbgCtwLc99vW87T+54asMF/5Zvv09n+qA8f1UsmkLiRzEjOMrSwFk6uKgrSBh7rCTgnaPNA4u8Zkh4TPP41pEYN1cLZwXjO2pEuZl9GGWxVQfZ8Zzlb9bNEP/Uawihjjk3Qtd2tis/r6uAg/Mv6TWSRKZN0szR9DV3xP2h7baVXv7irK+zCLT1RB9s62EeYCGSuvU8Ggjzy0R8d7GY3+P2v6TG76SGPfRBf+Gx2cjtdF0qXaAEZvyhxnG8vqgLeLpGUj8S/m9J1J+bz6P4+A7gMGHqS1KLidJfg1pNNkvwSBEkb+r5Jjn/oaEEcizeBwPfBS/bCD8RV0RVsiw10a/g8b5yhavJ+ozR1OksL5IhbS/pEsJ02qkKbjg0wKDppHAtZGR6wSh3JiwD4Tb0xp8fnUK4jrMsadj/RtT1hfnW2PIQYl55k7dGC4I2hbSWt+M/Uo0vphNfCbwvt94XW741Lqu4PnZpkpx/mStQpboahK2r+Hp/scqCXkiw3eXKumYziLJy/w1QjTFko8I7Gj6Dcz0+wTtyKCdIGH92e94JoVoHa9p8LkrWfDKKe/hcuvYUlrrfrWtnp/9Iw2/dapdrVvyvSCvmTRPLW2XOXkLx3yNw2hpHOiZxow8LKb/0qZA8jHJCfj4iS4kHK7PlFBt2q7AFtT9m5VkfI/y2BdO1feJIS+e8NjXqgU8X5WJIQfBbSm/882gHeLh3tNUI3CiZNeiLFfScfXlmB/1Ufah1YwetEWn3kAbuHeTv3nacW0JSee6dIvjGuboDVr0znnW3zf5m8VKNEeLyAiSR3C5K2ivRK6hoNolxT7815jPqPaTVEmzYoxQUrqk1j6IIb58OHM/ptLZbyQsU1UWExgaiN5tfobt9dDzBfyvvhBDXjzpsa9vFfB8Va53PTbl87P/nOdp3yBq8NKcfQwu6bgu6qmfqNDhIxglav5/qcBxwBROovN3mvzdvz2s15dj+jmsRe/8jATvvXeJ5mhejbbv34JLjktrmDenIRgtydOeuYJO8CUvY/neXMTwDD1cT5D06vlWAZZ+dBvvjxT9B899jjZO5wX/cUiSeYjhLJ6f7+MKj+2dKf9+uAqZeYHfkI/KMWuXdFx9aTKjGsPXPPQ5KPLvpwtct5tIMj/rOKvARinveYHjGtaoorWGuEON7Gb7ct8C9kKXoLhmivX0F3GnLkqq7YurunJZ2V9GFmJ4YEUm2tG6kbRDcsJBeX6PfSIh3yQGX7jLUz+YT75jw/k/pEm3g5bwCA/3xGx8qId+8I9epqTj6sOXFX+pqMbQR+JsUrjUm7ofK+D3P6oH+oMJ/56IUVeEf9oMERALV+ob0uQUlQNykxgh5+JuuJ/k9YElmO8ex/WkWkOsH3+KXPuXJC91ieIgqlFH01h6JU8WYliVlCn8NhyU12rhPTnszhe/KWpENwqrkewPYz32dYAN5/8I2iMp/p7i9j6SWOML9rKHfrYq8dj62MOed+wh4z0JwvW5aH1GWuI6c5L+/pc8rPHBKeccUdu/clwfpEStp+f3TG5NUrlFzaq3VkAx4CLKaTR+XybsMy1GOa4NT8F9ouboNClmhsUoJV4v+bvMRAyvrdBhhQPvbVKML1gUfXSz2Mtzv5PEj5nMMAM+K4xsKqHrQncHJCNNFOJunu57i6d+dizpuJLzz4fp0mXi9ZX+ql4QJh1UXj/eKXqgQ8COySgUx63xPVL2c7q4tY/Ml8vFT8YNCCaBitfLzDke0X7uV4H13yvmPSaFi0Q2C2jZTgWAaIPM1bTsf3bsS2jxNkz4XA/LjLrnBLglTVeEi9HOjutXVOBdZiKGV0u1ADkk59P3pLjgGJyE8a/as4C+T5FyVCzoJLwqoUnAB5hTv7Yh/d/mmfQg3M7TfR/x0Mey4l/L7wtoMn3keHMRQ3zxfAS0Rd/lJTne5RF6cO+l6zQrSD78WQwxTHPufa77umucdlUismyO56SGL+bOkxzPNVUFqDe6wf7xvuPaoCb77jEqNEXbiLo5SMDSDY7vj0jxbKP0v3dK8jy4W8nMUfDMpWs6lRiymTxXsUmHap5gkFtzLmLXAbefjsl6BTz3CyqxGvzjTx77wiF9eDcfzxdT/C0mvbk93ddHreMfl3hcD/LUj0s7OEX8aA3RmNebu8+RMFMFWjv8o6MRnBA2zP+Ync9Q8oNwjUka062PlE2k4rnZcZ0cgDuk7Ashcp+Yz9aRUKN4ZhMiUw+0SZiNCW54WPtwgXveXuE9oUfO/aNRsBBm2kaVaAY5iF09mANJTdVohqdJ/hJ4zMdJnUoMJYaBVwGb6iI+J8UijiOaw3RTJd1G/wKelc10X6l2+pIy4xpJnnIgCc4Rv3W/q4Y0Wo01Pd43b3m1QQ0O/XaDQIT1Pe0ld8d89jdPz1pPrjELHq4H+wJ6zsylwkBPfWckE8fP9IcSmucmpLzfSGnuInRVzPUsKWeIJP1+g/PgIFWYPKZkF4UBVa+GSJiomuclxya58SgLSyR9XIk2CPuekq20ZLvgyh2aJm+ky/1ghM6fKNhn02T9QNMXjcgnYGr7hN9/U9/XdQn/foC4cylfXpWXmZUYXiPVBYuYgIFndbNkseOQ3MzM3E83aiTD1/QlF5n37LfiL3rWMDN4h7d67G92PWTX7KbjmYYY+kwknccCAEm5UPwHEfhAfxU6fQAzcpxm1ZeP5k7SOEgGTQkuMT6qPaBt+2OC84vf5sp5SJWdLMFGCH8kSG6UGJwUbofou0OBgtYU30H8z49W5cQcTUgIhPrSiq3//8bsiUnh2ovnUlK3qZIttMoU0xgr6RJqQ1pdvn1pXL+GS/KE8PgWRn1PP5D4hNmlQ9ZayajWx0t5S0glQZdK4zWJ/EOV+PhdVBn5TMngQD18lmrhAUKt1yONuxUOamFu4bG/eXXTwk/qrAyHIJLwShUdy4kp/nZxj/elTFnWiM2TpRgXEBfwm0uqFeur2oklPN37H0328gnip071JTqHi8zFiVB/dkKlxucqyJ8UI3jjh5jWxxIfe5ITnyuhNtAXpqqQ8lOppk+5y0S6QIrvs2/iv7dg5PrXEhAqvkfU/QZN5mZUU7yhks0kUcJp5rTLjHyV+CtQsZCSZUzp8+k5w/77rM7p3Enm85Stq7LWUGKkEyRe/F2oQ3mgShSbKzFsFSl8RUL/B0tPUzyQRp/w3CdmMkxJmJSGSTI/Fg7lUyXMAzh3RccyzWHmMwfccMlWGeSn2loFNB2XOw6+KNCmYinYyOO9G7n+TFdC4gPLqWamiL0SDSr5385JeW6dq4J+FOzpWX1Ln9P3gyny4Zy/i4jZi1Ug3F+qG2j4tuNaGsURxPj4DPedovtss3HDhSwaqNYl/n3Dyevpqnjmw4y8tAqME3TNHqRzEA3lwSowvajkMFemjDzEsMi0NTBrVMs/k+6V2Jk6o5ir3xVDq3BUQf0O1s0ASe42JX6YmEaq4MGhhJ/MOAnN2vy7V4XHMc2BNqvH+9IXWpykkbv9dFP9ZRvGiANsvGov+P819fBcWQXSq/UAW93jPd+R5nk7LxQ/Jl6AX931Hsl/l2pgnpZsKY7eVzLpAkQka73z6fo7eYff0Pn0oCSr0020NcFvI1RQ2FuKqxjTKrhcSdJWNTo/JYGC8JM2KGn6MZd5foTncdjdcY1Aq/ty9otlBE31dvp7d9O501vPjQV1LG5SDjFOcqQEy3MQcePXxV8dz4+V6f5ZSeGkuo2BQ/VH3YAUIoW+YFytpSBSbIxnDU2UuAzV1slIQywme743pez+qaQ7rk7uHEowEAQWbuM4zaqH0YgW3e/qBGSFfRwL0C6e7on/HoF5+0v2hNc9lGQeIfn9dnEZIP3NgMh1/MCuVHL4UY7+H5cZtW+JOF5SCf9sOu8+0zn/jmp0skSmzlLy9e8KHllcCUvSFC/TdQ0jPB3eRIBkXqExS5Mma7RyiXres5zOr4c8jcMeMffNE+jI2YSm8HMlf9dG5gWKtLf1Om2IrufLlVekjmzPQwyn683zELZJejDzo3Hc/zTmPmhTcG49rkMPVTRGm3WA1FhVMIfxteppQ5EZaTSG7xVwf/xtMN+P1UPjbSUXi+rBz2bZtxu+lz8m/Lvj9dDxtQYoL4irBmbxc3WfT+KntbRqOiDOvlKLfaAE0zUWy+rBvZ0k0/Y1A4f3s5K8bFoS8E4OLfk8e1bHLzp/KBk6KkU/U/Wcx0d7exX6FtN+31PB78Y6Ip4GWG/+KjMHHo30RAzR9Lsq6+SpjYzmvabpxB+egFm0hBfo2NZK736pwtiPdQ/cRtfeRRJmXkhTfCC36SoLMeTlXK+NjWNKio1rvC7usktPacCE3DaFVGXwDzYZ8qdZwE/rDpEigHVhQ0le1aDTcZskr0LCO8HEvbfnZ6glHYYwPaINQRh3GTRpaNQI2iLIYF0pLqCRgxSN6MaOzyAKRBHvK35TWPkCQROuiO8yJb7GrDvO8Zy7SLa0OxP1nZzn+TkvdRDDXZR4500N5yqBx5jkyfuM1h0Lx8kyI+VULYk3ZA8T9Rcq4BBtTyT+gnodF4qD9bkuaCUxxKciiTmZv0EreK0+8NQcL5WNDnPzkh0izR8ilquwDDhBwkivVW0oCsf9NgQtwW9S/v3hqpWYv4BnQZhfR+KTOReNmpkSrfI8js/x+0UrtY/40Rz6wjZKClwKhTtKNt/udBBDtFporJ4vyTOiuUaDXB/kN0Dn/XU5+mXuuHz68gadoMVHefbbumvf0P+eXDc30CxitZlTuRG+yucqMdwqLTHs4WGxXR/z2XOqhYHF4mtAItN7PCw6JE6ctS+u8Ib9rr7w/Y0Ulga8B3Kx/ceGonA8IFbmsWj8LQNxeF8Pkk4F0ZyUsYvzh4U4onGZqyTPiyn1Gsc5jdnw+1I+7aarXG5XDLFt5z7vqnec1+f32w6BKk1tZRf6KNG+T8lslBg+UHdtHiWnzImaJhnXNDSvqX10e3gYaHLBvab/j0nuWAkjMrG149cxroAJjL8hJg8c+l+UauF8HZtrxVA24KpgqYKKxxSpUBWACgLhO6vvN0Tk7A4eG8jy9xp8jtUAX6112viMkKmj9F30dnxOjeB/lXBsH495rh21FTleabTcruhkNIbz5ngGV9AJrhzv5OhzPv1t0byEK+t/H1MOt04dn7gsInTDzQa2gxi+IqFfyNzKZCn782QLFzkh8T8WdxHuMgFHUPIb7Rdh/4ZyYaxqFapODv9b8uc7zQh4YSCh8zM5vo915x8V+J1ZFQ648PyswedLSBjlTj7SVmsPCdoZo+/QdT6TjuQ3JX4np8ZcJwhirQLuh1YNZcvaKb6Dpi2a/QO3ut0zPgMVXrZzXM8r/PaOuYZiaYISWSrloFEksfx1DqGnp2QIKOvh6eVMlfaZhnC8/J0uZhZ72XIAYj7HGX6I5M9lZGgNrlMJt6pmfqL9y54eZ7wevGXCO1J9E/e9KpznwZd60P2rxL/z5pz7KXkHf9CAXHI24v+NRQrL15wF/x40XqdL6Bs2pIHQupuUywcyCszJ4xzXCTJCg7alx3uRiQB/5e86PmvmEnSJ41pWc/K2MnP5PyLwb8j5+whIxRw9qO7aICWxj9XNG7CDtk8ic5iI7tRBSj2kc/CRLnYGYq82k7CPVIph4n5LmieYNZQPSOYbS/WSjT+hkvmDFXhWTGJPluRZ3lAyPanCcxbtwS6eiMMknf9lDBS6XGY45efB75UAN8phiO/WKRKmPxolYQnFLk+/g/OXCFP85V9Voton5m/JRbeNuKu4NMOGSiSStLz1fCEye8e8mzmV0F+q53RWYEq9UgUXV7Ag0bq7J5hDUaEArdvXMjyPy4x8g+QvDUnUPm4NaENrZu7B+l/S9hDHMVr/7SoHSPqcASosdltiWMPnuoAx25K88liJT3rrE5jurtFJQi3D/epYvaG62hc2i3sqMu+JrF5D8qVHaCU+Uw1Cu9NuPKzj9kSB93in4N9AjrdNPY/lJCUVF5RkvnDgUyuZMma+6s7eqAfoI03+jmTVe+pegLBI5ZJ9lJgkrfKCGXBFVVyM0neF0mCkNE7Bdo6uk6yJ4edUMpWkLe9JON2nwee8PywG10mykpa9dR/+ma7Vx1QA6oqZI4zVA036hIjf5bi+Z8rfOr+4rTOXeZqfEL9eKjRIHXGt7VW40aEdPVhmTtN1WB0JToWu6dPLmLapEJDb59sq8a2tA5wnXQ8b8UM6Ae/TDeMLMXQiamalE8VvnV8fmKKbBwLQaym/i9m0v6fnmFuym2GXUU3FMi0eOzRrZE44vk7D8YqEWRTyonb414Cfz7lNDsysoOTWZgULBJAX/ELbFbH7Fwnr179aUP+99SBFi52lZOMEbWgfP9a1MKsSyoFKIBaXdP5enDFU97gq5XvynbGDMV8iw/d+qHMmCd7X+7xdtxbZmwjAGJTwrGYdbJ9CIcMavShy7QX5qum2GSBspzuEwIXFj+YeE/Uzyl9I/UNQGYEyaA5r1ha05yiliEAmF+iLulbOUo5CcEqqkpfdiRi6NgKkIzLtL6YDP0BfRO3wn6xajU90kb6ikg7/fdP4UrcDi/0klXLbXSXlNZUEz9EDKQvKQgxr379AN/ZWAH8n8vZFKygURQxrYMPGJ7qPp9+BiRErRStcHubXZ9+9hfP8LiXuY1t0P7RXJ7R5jU9VwnKUHvZpCXxZiGGNtJzfAoGCtVbTnqVZo1FiiI9kmtKQEK81Itcgij4r1ayn67wmsOAD3Fe+aq7/nd7zA30mSCQaf1LVpM4h2Z2JocGQFUuppMgmPGcL74sw8lclhHenlQJLTgxrIBkrWrwVCjpwb9GNO45oFE0MAdaK83TDzwo0LGi30EK2ehMfrKTFZwm9euAWca2O0d1tWuNL6kE7wuMaaYYvdG0T2Zs1qrxsxBAsIqH/PwJFl+dnI3qeEnpZ3H3YA6K+efhx3pTw+7iquao4QRTHef6dGyhpRYP6sMycm7CnKgn21X+/pEJ2Jjc6I4YGQ3ZgJtpaQgf2TQs4QNiQH9UNDInRdy1tIhx9lZe8XCVZH8B0j7mEaENMpHm0a9N1kyYR/yXSXNPfCmJYA9GMaC3T5MyboBqYM6T9gTIL6RziwM9bMQgf7TEq+Fwl5YkO76trHBI8tABBkPmJhgd3EPwWJ+bsb0XxnzMQa9lZHvpB2KOoAyXa8uQMZN5fp0JR1hrHEN3xEaKKtnEBSR7UdIIKZ/V4tiChFvTT/fARmTm3YQ3rS6id/bvkcG0zYmgw+EFP1aQQEUw+T2pX1lwUXH6Jn+rCZdN9Sw98zMOUj8Sx+HGx6iC1zXCIhL4zK0noh4gGIpoeYqqO5Zu6aT6pG+i9ks681EpiWMNgJYk4jxMAMCByWCEQEB18m2pIypiuBD+69fQ9Dap7T1EiNUVmuOXgC/WYHu4PSvnzWvZSTQ2/kYAVNL+4I/VO0cdk1eLwu/+pZHhiN1vTPVSQWF9mBLwspKSsd4Q0T9Q1/byu5/t0LeQVQo9RYlcPiOb3knIn3WeipXmPlnJVejFiaDCUdZ3JDG0iZPATGxKvY/q5Em0faAcxdBGQfkoipnXAu5pdD3yf76lM8xDfy4Xlqz7qvXSds97RhhJU8YYJe4nny+fizwLhemfPy8zBbutJ8tQu68b8LW5GL1f9BfSyOWgwFI7pdiDYmKbAlx322z7u8Hn4tjZDNebLOg5SiECYJvfxcMe1ezuBFIIeNgcNBoPBYDB0E4x0XBstyYO40H7v7Lh+WacMkBFDg8FgMBgM3QGkfHGlo0mTBHpz+aofMMBl4BojhgaDwWAwGAzVAUFe0YAoMj+kSQ80zHGNaPr/dMogGTE0GAwGg8HQHTDCcS2NtpCAt61y9mHE0GAwGAwGg6HNqJWVqwdR/1em6GMHmblkInkVbzZiaDAYDAaDwVAdDHNwHvJIpilvu4fjGr6FnxsxNBgMBoPBYKgORjqujU7xfepoD3Fcv6LTBsryGBoMBoPBYOhkUGnl647rlPo7LWEflA+N1nqmUtXdnTZYRgwNBoPBYDB0MvaMud43Z79oC6d12mCZKdlgMBgMBkOngoTUuxfU9+WdOGBGDA0Gg8FgMHQqSEg9bwH9Ph60pzpxwMyUbDAYDDMw3YbAYOgorB+0Vwvo9+xOHbCu6dNtHzQYDAaDwWAwmCnZYDAYDAaDwWDE0GAwGAwGg8FgxNBgMBgMBoPBYMTQYDAYDAaDwWDE0GAwGAwGg8FgxNBgMBgMBoPBYMTQYDAYDAaDwZAI/yfAAH4ZRXlJ/vl0AAAAAElFTkSuQmCC", "MIMEType": "image/png"}, {"Name": "图片1", "ImageData": "iVBORw0KGgoAAAANSUhEUgAAArsAAAChCAMAAAGHaLfyAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAPUExURQAAAP4AAP4AAP4AAP8AAC2GjgsAAAAEdFJOUwDPVI+KsLEGAAAACXBIWXMAABcRAAAXEQHKJvM/AAAFMUlEQVR4Xu3d227cOBREUSfd///NESVKIinqYvtUgxXs9TA2BoFwsFHuGbcT5EvvlT9G47kLo+e+0z8U96YHSzpMD9b0fX/9zZ8FE16sa6xbheTJy4NFnROeXNI92Y2oBI+d8NiJ12P/5I/hRPfy4AIP3vg9WM7r8pfVuVyrw7Xh8ndbNm3X7w5dlrB8/+2z2+leq68yw3vd9pB4fb0lJhdv95pcXNxr9pU342I9w4vzR2hULxrje3vd+/bqOx3rdG+61eje+VSfe5dLbe7Nh7rcu95pcu92pse9FlfuR5pF9Th3O9Pk3PVOl3PzoTbnLpf6nDufanRuutXp3OlYq3MN+3odPN9qdPByqs/B+VKbg9dDXQ7e7jQ5eD/T4+DiSouDyyO3zz1S73e63LseanNvvtTn3uVUo3vnW53uTcda3Ttd63Wv288v3Pbwf+BnnkIvuy9BI6/3i1cMlRSXF2SRJS55Jda45BXY45I3XBmXvMHquOQN1cYlb6Bj3CkvfiLXK/Ti4keOeYkbqM1L3FB1XuIGK/MSN9yel7gCa17iSix5iSuS8hJXhrxSvDhI8Z82qTUvgSX2vAQWKPMSOFydl8DB2rwEDnXMS+BAvbz9wPmHG/ieXK9xDHzyC/EzbWDyBqsDkzdcGZi8Antg8kqsgckrsgQmr0wKTF4h/uiKWPk/aYAVXhuEeOUVOn3jB793/rYafm1KS12VVJa6InNY6mosXakrkbNSV2GtSl2BLSp14+1NqRuuSErdaGVR6garglI3Vt2TuqGanNSN1NakbqBDTOrGObakbphOyvT+Ob4t1yux0yidkMQN03sNyJ/g1w4piRuobUncSE1M4oaqaxI3VpWTuMHKnsSNVgQlbri9KHHjbUmJK7A2Ja5CjkpciaUqcTXmrMQVSV2JqzKFJa4Mv2dfie0qpbLkVZnDkldk6UpejZyVvBJrVfIqbFHJK7A3JW+8Iil5w5VFyRutCkreYHVP8sZqcpI3VFuTvJEOMckb6NiSvHE6KckbpleSvFG6ITt5859hwffkerXjv2bPgQ4xqRuprUndUE1O6saqe1I3WBWUutHKotQNVySlbry9KXUFtqjUVVirUlciZ6WuxtKVuiJzWOqqpLLUlZnSUlfn9I1gRKCuFHWlqIv/HX+JIDzx94vC07RctgtD83LZLuyk5b54PwJ2luXyXhrcrMtlu/CyL5ftwkm5XLYLH/Vy2S5ctMtlu/BwXC7bhYPectkuxtdfLtvF6M6Wy3YxtvPlsl2M7Gq5bBfjul4u28Wo7pbLdjGm++WyXYzoyXLn7QIfkkd349ly2S4+KY/u0tPlAh/zaLssFwN6sF2WiyHdbpflYlA322W5GNbldlkuBnaxXZaLoZ1ul+VicCfbZbkYXne7LBcGOttlubBw2C7LhYlmuywXNqrtslwYKbbLcmFl2y7LhZm8XZYLO/N2WS4MsV24mrfLemEob5f1ws62XdYLM8V2WS+sVNtlvTDSbJf1wsZhu6wXJjrbZb2w0N0u64WBk+2yXgzvdLusF4O72C7rxdAut8t6MbCb7bJeDOt2u6wXg3qwXdaLIT3aLuvFgB5u9/l6p18GfEge3a1n612eCXxCHt0DT9b7rQcCH3O/XraLUd2tl+1iXNfrZbsY2dV62S7Gdr5etovRna2X7WJ8/fWyXTjorZftwsNxvWwXLtr1sl34qNfLduGkXC/bhZd9vWwXbtb1sl34WdbLduEorZftwtO83vw54GVab/4McFP+pALQ+fr6B/MWS9YVuzPCAAAAAElFTkSuQmCC", "MIMEType": "image/png"}], "Layers": [{"Name": "default"}, {"Name": "Layer1", "DesignerLock": true, "DesignerVisible": false}], "CustomProperties": [{"Name": "PaperOrientation", "Value": "Portrait"}, {"Name": "DisplayType", "Value": "Page"}, {"Name": "SizeType", "Value": "<PERSON><PERSON><PERSON>"}], "Page": {"PageWidth": "41.6cm", "PageHeight": "19cm", "RightMargin": "0in", "LeftMargin": "0in", "TopMargin": "0in", "BottomMargin": "0in", "Columns": 1, "ColumnSpacing": "0.5in"}, "DataSources": [{"Name": "DataSource1", "DataSourceReference": "..\\Data\\ArsDemo.rdsx", "ConnectionProperties": {"DataProvider": "JSONEMBED", "ConnectString": "jsondata=[\\n    {\\n        \"ID\": 17,\\n        \"Code\": 17,\\n        \"EB_OWNER\": \"李芳芳\",\\n        \"Col_Num\": 3,\\n        \"EN_DESC\": \"燕之屋\",\\n        \"Z_C\": 90\\n    },\\n    {\\n        \"ID\": 5,\\n        \"Code\": 5,\\n        \"EB_OWNER\": \"郭旭东\",\\n        \"Col_Num\": 1,\\n        \"EN_DESC\": \"318好邻居\",\\n        \"Z_C\": 92\\n    },\\n    {\\n        \"ID\": 12,\\n        \"Code\": 12,\\n        \"EB_OWNER\": \"代明宏\",\\n        \"Col_Num\": 1,\\n        \"EN_DESC\": \"瑞表\",\\n        \"Z_C\": 93\\n    },\\n    {\\n        \"ID\": 14,\\n        \"Code\": 14,\\n        \"EB_OWNER\": \"代明宏\",\\n        \"Col_Num\": 3,\\n        \"EN_DESC\": \"欧米茄\",\\n        \"Z_C\": 93\\n    },\\n    {\\n        \"ID\": 2,\\n        \"Code\": 2,\\n        \"EB_OWNER\": \"王文超\",\\n        \"Col_Num\": 2,\\n        \"EN_DESC\": \"T3D倍轻松\",\\n        \"Z_C\": 95\\n    },\\n    {\\n        \"ID\": 15,\\n        \"Code\": 15,\\n        \"EB_OWNER\": \"李芳芳\",\\n        \"Col_Num\": 1,\\n        \"EN_DESC\": \"同仁堂\",\\n        \"Z_C\": 95\\n    },\\n    {\\n        \"ID\": 11,\\n        \"Code\": 11,\\n        \"EB_OWNER\": \"李丽萍\",\\n        \"Col_Num\": 4,\\n        \"EN_DESC\": \"奔驰\",\\n        \"Z_C\": 98\\n    },\\n    {\\n        \"ID\": 6,\\n        \"Code\": 6,\\n        \"EB_OWNER\": \"郭旭东\",\\n        \"Col_Num\": 2,\\n        \"EN_DESC\": \"爱慕\",\\n        \"Z_C\": 99\\n    },\\n    {\\n        \"ID\": 1,\\n        \"Code\": 1,\\n        \"EB_OWNER\": \"王文超\",\\n        \"Col_Num\": 1,\\n        \"EN_DESC\": \"书刊1\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 3,\\n        \"Code\": 3,\\n        \"EB_OWNER\": \"王文超\",\\n        \"Col_Num\": 3,\\n        \"EN_DESC\": \"木九十\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 4,\\n        \"Code\": 4,\\n        \"EB_OWNER\": \"王文超\",\\n        \"Col_Num\": 4,\\n        \"EN_DESC\": \"305箱包\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 7,\\n        \"Code\": 7,\\n        \"EB_OWNER\": \"郭旭东\",\\n        \"Col_Num\": 3,\\n        \"EN_DESC\": \"SKAP\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 8,\\n        \"Code\": 8,\\n        \"EB_OWNER\": \"李丽萍\",\\n        \"Col_Num\": 1,\\n        \"EN_DESC\": \"335香化\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 9,\\n        \"Code\": 9,\\n        \"EB_OWNER\": \"李丽萍\",\\n        \"Col_Num\": 2,\\n        \"EN_DESC\": \"书刊3\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 13,\\n        \"Code\": 13,\\n        \"EB_OWNER\": \"代明宏\",\\n        \"Col_Num\": 2,\\n        \"EN_DESC\": \"法蓝瓷\",\\n        \"Z_C\": 100\\n    },\\n    {\\n        \"ID\": 16,\\n        \"Code\": 16,\\n        \"EB_OWNER\": \"李芳芳\",\\n        \"Col_Num\": 2,\\n        \"EN_DESC\": \"书刊10\",\\n        \"Z_C\": 100\\n    }\\n]"}}], "Body": {"Height": "11.781cm", "ReportItems": [{"Type": "table", "Name": "Table1", "ZIndex": 2, "DataSetName": "DataSet1", "DetailDataElementOutput": "Output", "TableColumns": [{"Width": "1.666667cm"}, {"Width": "1.666667cm"}, {"Width": "3cm"}, {"Width": "2cm"}, {"Width": "2cm"}, {"Width": "1.666667cm"}], "Header": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox36", "DataElementName": "TextBox36", "CanGrow": true, "KeepTogether": true, "Value": "源数据结构", "Style": {"Border": {"Color": "#bfbfbf"}, "FontFamily": "微软雅黑", "FontSize": "14pt", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#103340", "TextAlign": "Center"}, "Width": "0in", "Height": "0in"}, "ColSpan": 6}, null, null, null, null, null]}, {"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox14", "DataElementName": "TextBox14", "CanGrow": true, "KeepTogether": true, "Value": "ID", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox15", "DataElementName": "TextBox15", "CanGrow": true, "KeepTogether": true, "Value": "Code", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox16", "DataElementName": "TextBox16", "CanGrow": true, "KeepTogether": true, "Value": "EB_ OWNER", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox23", "DataElementName": "TextBox23", "CanGrow": true, "KeepTogether": true, "Value": "<PERSON><PERSON> <PERSON><PERSON>", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox30", "DataElementName": "TextBox30", "CanGrow": true, "KeepTogether": true, "Value": "EN_ DESC", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox33", "DataElementName": "TextBox33", "CanGrow": true, "KeepTogether": true, "Value": "Z_ C", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "<PERSON><PERSON><PERSON>", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}]}]}, "Details": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox17", "DataElementName": "TextBox17", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!ID.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox18", "DataElementName": "TextBox18", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Code.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox19", "DataElementName": "TextBox19", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!EB_OWNER.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox24", "DataElementName": "TextBox24", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Col_Num.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox31", "DataElementName": "TextBox31", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!EN_DESC.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox34", "DataElementName": "TextBox34", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Z_C.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "=iif(RowNumber() Mod 2=0,\"#dff2f9\",\"#f5fbfd\"）"}, "Width": "0in", "Height": "0in"}}]}], "Group": {"PageBreakDisabled": "false"}}, "Footer": {"TableRows": [{"Height": "0.75cm", "TableCells": [{"Item": {"Type": "textbox", "Name": "TextBox20", "DataElementName": "TextBox20", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox21", "DataElementName": "TextBox21", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox22", "DataElementName": "TextBox22", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox25", "DataElementName": "TextBox25", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox32", "DataElementName": "TextBox32", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox35", "DataElementName": "TextBox35", "CanGrow": true, "KeepTogether": true, "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt"}, "Width": "0in", "Height": "0in"}}]}]}, "Left": "17cm", "Top": "1cm", "Width": "12cm", "Height": "3cm"}, {"Type": "shape", "Name": "Shape2", "ZIndex": 3, "LayerName": "Layer1", "Style": {"Border": {"Color": "#f4b084", "Style": "Solid", "Width": "3pt"}}, "RoundingRadius": {"TopLeft": "0.2266661cm", "TopRight": "0.2266661cm", "BottomLeft": "0.2266661cm", "BottomRight": "0.2266661cm"}, "ShapeStyle": "RoundRect", "Left": "0.9789583cm", "Top": "1.719792cm", "Width": "19.2185cm", "Height": "0.8754cm"}, {"Type": "shape", "Name": "Shape3", "ZIndex": 3, "LayerName": "Layer1", "Style": {"Border": {"Color": "#f4b084", "Style": "Solid", "Width": "3pt"}}, "RoundingRadius": {"TopLeft": "1.25cm", "TopRight": "1.25cm", "BottomLeft": "1.25cm", "BottomRight": "1.25cm"}, "ShapeStyle": "RoundRect", "Left": "24.10354cm", "Top": "2.407708cm", "Width": "7.1cm", "Height": "3.8cm"}, {"Type": "textbox", "Name": "TextBox37", "ZIndex": 4, "DataElementName": "TextBox37", "LayerName": "Layer1", "CanGrow": true, "KeepTogether": true, "Value": "* 该模板按行展示了客户经理下的所有门店及门店销售额，并计算出所有门店的关键数据。\r\n实现方法主要通过使用 ActiveReports 提供的矩表控件转换为纵向动态的列。\r\n", "Style": {"Border": {"Color": "#f4b084", "Style": "Dashed", "Width": "2pt"}, "FontFamily": "微软雅黑", "FontSize": "16pt", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Left"}, "Left": "0.5cm", "Top": "3.280833cm", "Width": "26.5cm", "Height": "8.5cm"}, {"Type": "tablix", "Name": "Tablix1", "ZIndex": 1, "DataSetName": "DataSet1", "RowHierarchy": {"LevelSizes": ["0.787402in", "0.984252in"], "Members": [{"Header": {"Item": {"Type": "textbox", "Name": "TextBox8", "DataElementName": "TextBox3", "CanGrow": true, "KeepTogether": true, "Value": "=RunningValue(Fields!EB_OWNER.Value, CountDistinct)", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "Group": {"Name": "Tablix1_EB_OWNER1", "GroupExpressions": ["=Fields!EB_OWNER.Value"], "PageBreakDisabled": "false"}, "Children": [{"Header": {"Item": {"Type": "textbox", "Name": "TextBox3", "DataElementName": "TextBox3", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!EB_OWNER.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "BodyIndex": 0, "Group": null}]}]}, "ColumnHierarchy": {"LevelSizes": ["0.2952756in"], "Members": [{"Header": {"Item": {"Type": "textbox", "Name": "TextBox6", "DataElementName": "TextBox6", "KeepTogether": true, "Value": "=Month(Today())& \"月客户产品主责店面管理及得分\"", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontSize": "12pt", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "BodyCount": 2, "Group": null, "Children": [{"BodyCount": 2, "Group": {"Name": "Tablix1_ColumnGroup1", "GroupExpressions": ["=Fields!Col_Num.Value"], "PageBreakDisabled": "false"}, "Children": [{"BodyIndex": 0, "Group": null}, {"BodyIndex": 1, "Group": null}]}]}, {"Header": {"Item": {"Type": "textbox", "Name": "TextBox1", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "平均分值", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "BodyIndex": 2, "Group": null}, {"Header": {"Item": {"Type": "textbox", "Name": "TextBox10", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "最高得分", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "BodyIndex": 3, "Group": null}, {"Header": {"Item": {"Type": "textbox", "Name": "TextBox12", "DataElementName": "TextBox1", "CanGrow": true, "KeepTogether": true, "Value": "最低得分", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}, "LevelCount": 1}, "BodyIndex": 4, "Group": null}]}, "Corner": [[{"Item": {"Type": "textbox", "Name": "TextBox9", "DataElementName": "TextBox7", "CanGrow": true, "KeepTogether": true, "Value": "行号", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox7", "DataElementName": "TextBox7", "CanGrow": true, "KeepTogether": true, "Value": "客户经理", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "White", "TextAlign": "Center", "VerticalAlign": "Middle", "BackgroundColor": "#309fc6"}, "Width": "0in", "Height": "0in"}}]], "Body": {"Columns": ["2cm", "2cm", "2cm", "2cm", "2cm"], "Rows": [{"Height": "0.75cm", "Cells": [{"Item": {"Type": "textbox", "Name": "TextBox4", "DataElementName": "TextBox4", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!EN_DESC.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "TextAlign": "Center", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox5", "DataElementName": "TextBox5", "CanGrow": true, "KeepTogether": true, "Value": "=Fields!Z_C.Value", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "Format": "n", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox2", "DataElementName": "TextBox5", "CanGrow": true, "KeepTogether": true, "Value": "=Avg(Fields!Z_C.Value)", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "Format": "n2", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox11", "DataElementName": "TextBox5", "CanGrow": true, "KeepTogether": true, "Value": "=Max(Fields!Z_C.Value)", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "Format": "n2", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}}, {"Item": {"Type": "textbox", "Name": "TextBox13", "DataElementName": "TextBox5", "CanGrow": true, "KeepTogether": true, "Value": "=Min(Fields!Z_C.Value)", "Style": {"Border": {"Color": "#bfbfbf", "Style": "Solid"}, "FontFamily": "微软雅黑", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#404040", "Format": "n2", "TextAlign": "Right", "VerticalAlign": "Middle"}, "Width": "0in", "Height": "0in"}}]}]}, "Left": "0.5cm", "Top": "1.727618cm", "Width": "5.708662in", "Height": "0.5905511in"}, {"Type": "textbox", "Name": "TextBox38", "ZIndex": 5, "DataElementName": "TextBox38", "CanGrow": true, "KeepTogether": true, "Value": "使用矩表行列转置", "Style": {"FontFamily": "微软雅黑", "FontSize": "14pt", "FontWeight": "Bold", "PaddingLeft": "2pt", "PaddingRight": "2pt", "PaddingTop": "2pt", "PaddingBottom": "2pt", "Color": "#103340"}, "Left": "0.5cm", "Top": "1cm", "Width": "8.6cm", "Height": "0.75cm"}]}, "DataSets": [{"Name": "DataSet1", "Fields": [{"Name": "ID", "DataField": "ID"}, {"Name": "Code", "DataField": "Code"}, {"Name": "EB_OWNER", "DataField": "EB_OWNER"}, {"Name": "<PERSON><PERSON><PERSON><PERSON>", "DataField": "<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "EN_DESC", "DataField": "EN_DESC"}, {"Name": "Z_C", "DataField": "Z_C"}], "Query": {"DataSourceName": "DataSource1", "CommandText": "jpath=$.[*]"}, "CaseSensitivity": "Auto", "KanatypeSensitivity": "Auto", "AccentSensitivity": "Auto", "WidthSensitivity": "Auto"}]}