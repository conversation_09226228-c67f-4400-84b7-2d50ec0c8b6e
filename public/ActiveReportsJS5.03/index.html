<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>ActiveReportsJS Report Designer Predefined Resources Sample</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <script src="dist/ar-js-core.js"></script>
    <script src="dist/ar-js-viewer.js"></script>
    <script src="dist/ar-js-pdf.js"></script>
    <script src="dist/ar-js-tabular-data.js"></script>
    <script src="dist/ar-js-html.js"></script>
    <script src="dist/ar-js-designer.js"></script>
    <script src="dist/locales/ar-js-locales.js"></script>
    <script src="dist/locales/designer/zh-locale.js"></script>
    <link href="style/Montserrat.css" rel="stylesheet" />
    <link rel="stylesheet" href="style/bootstrap.min.css" />
    <script src="script/jquery-3.5.1.slim.min.js"></script>
    <script src="script/popper.min.js"></script>
    <script src="script/bootstrap.min.js"></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="styles/ar-js-ui.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="styles/ar-js-viewer.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="styles/ar-js-designer.css"
    />
    <style>
      #designer-host,
      #viewer-host {
        width: 100%;
        height: 950px;
      }
    </style>
  </head>
  <body>
  <div id="designer-host"></div>
    <div id="viewer-host" style="display: none;"></div>
    <div id='onOpen' style='display: none; height: 100%;'>
        <div class="filebtn">
            <button type="button" class="mpen" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
            </button>
            <input type="file" id="uploadFile" onclick="upload()" style="display: none;">

        </div>
    </div>


    <script src="script/resources.js"></script>

    <script>
        var designer = null;
        //var viewer = null;
        var report;
        var designerOption;
        var viewerOption;
        var reportStorage;
        var isPriview = false;
        var customerTheme = false
        var version;
        var ARJS;
        
          var  viewer = new MESCIUS.ActiveReportsJS.ReportViewer.Viewer('#viewer-host',{ language: "zh" } );
         ARJS = MESCIUS.ActiveReportsJS.Core;
        var designer = new MESCIUS.ActiveReportsJS.ReportDesigner.Designer(
            "#designer-host", { language: "zh", });
        ARJS.FontStore.registerFonts("./fonts/fontsConfig.json").then(res => {
            console.log(designer)
            designer.fontSet = "registered"
        })
 

            designerOption = document.getElementById("designer-host");
            viewerOption = document.getElementById("viewer-host");
            reportStorage = new Map();
            //document.getElementsByClassName("mpen")[0].onclick = () => {
                //  document.getElementById("onOpen").style.display = "none";
            //};
  


                designer.setActionHandlers({
                onCreate: async function () {
                    return await waitForButtonClick();
                },
                onOpen: function () {
                    const ret = new Promise(function (resolve) {
                        $("#uploadFile").click();
                    });
                    return null;
                },
                onSave: function () {
                   // report = info.definition;
                    // reportStorage.set(reportId, info.definition);
                    return Promise.resolve({ definition: "20243-20", displayName: "20243-20", id: "20243-20" });
                },
                onSaveAs: function (info) {
                    if (info.id) {
                        downloadJsonFile(info.definition, info.id.replace("20243-20.rdlx-json", ""));
                    }
                    return Promise.resolve({ id: "20243-20", displayName: "20243-20" });
                },
                    onRender: function (report) {
          document.getElementById("viewer-host").style.display = "";
          document.getElementById("designer-host").style.display = "none";
                   viewer.open(report.definition);// onChange()
                    return Promise.resolve();
                },

       //   onRender:function (report) {

         // viewer.open(report.definition);
         // return Promise.resolve();
      //  },
            });
      designer.setDataSourceTemplates(resources.dataSources);
      designer.setResourceProvider({
        getImagesList: async () => resources.images,
      });
        
      var designButton = {
        key: "$openDesigner",
        text: "编辑报表",
        iconCssClass: "mdi mdi-pencil",
        enabled: true,
        action: () => {
          document.getElementById("viewer-host").style.display = "none";
          document.getElementById("designer-host").style.display = "";
        },
      };
      viewer.toolbar.addItem(designButton);
        viewer.toolbar.updateLayout({
            default: [
                "$openDesigner",
                "$split",
                "$navigation",
                "$split",
                "$refresh",
                "$split",
                "$history",
                "$split",
                "$zoom",
                "$fullscreen",
                "$split",
                "$print",
                "$split",
                "$singlepagemode",
                "$continuousmode",
                "$galleymode",
            ],
        });
      document.getElementById("viewer-host").style.display = "none";
            designer.setResourceProvider({
                getReportsList: async () => resources,
            });

 

      



        function upload() {
            const uploadFile = document.getElementById("uploadFile");
            // alert(uploadFile.files[0]);
            uploadFile.addEventListener("change", function () {
                const selectedFile = uploadFile.files[0];
                const reader = new FileReader();
                reader.onload = function (event) {
                    let fileContent = event.target.result
                    try {
                        const jsonData = JSON.parse(fileContent);
                        designer.setReport({ definition: jsonData, displayName: selectedFile.name, id: selectedFile.name })
                    } catch (error) {
                        console.error("无法解析为有效的 JSON:", error);

                    }
                }
                reader.readAsText(selectedFile);
            });
        }

    </script>

</body>
</html>