const resources = {};

resources.dataSource = {
  Name: "Northwind",
  ConnectionProperties: {
    DataProvider: "JSON",
    ConnectString: "endpoint=https://demodata.mescius.io/northwind/api/v1",
  },
};

resources.categoriesDataSet = {
  Name: "Categories",
  Query: {
    DataSourceName: "Northwind",
    CommandText: "uri=/Categories;jpath=$.[*]",
  },
  Fields: [
    { Name: "categoryId", DataField: "categoryId" },
    { Name: "categoryName", DataField: "categoryName" },
    { Name: "description", DataField: "description" },
  ],
};

resources.productsDataSet = {
  Name: "Products",
  Query: {
    DataSourceName: "Northwind",
    CommandText: "uri=/Products;jpath=$.[*]",
  },
  Fields: [
    { Name: "productId", DataField: "productId" },
    { Name: "productName", DataField: "productName" },
    { Name: "supplierId", DataField: "supplierId" },
    { Name: "categoryId", DataField: "categoryId" },
    { Name: "quantityPerUnit", DataField: "quantityPerUnit" },
    { Name: "unitPrice", DataField: "unitPrice" },
    { Name: "unitsInStock", DataField: "unitsInStock" },
    { Name: "unitsOnOrder", DataField: "unitsOnOrder" },
    { Name: "reorderLevel", DataField: "reorderLevel" },
    { Name: "discontinued", DataField: "discontinued" },
  ],
};

resources.dataSources = [
  {
    id: "Northwind",
    title: "Northwind",
    template: resources.dataSource,
    canEdit: false,
    datasets: [
      {
        id: "Categories",
        title: "Categories",
        template: resources.categoriesDataSet,
        canEdit: false,
      },
      {
        id: "Products",
        title: "Products",
        template: resources.productsDataSet,
        canEdit: false,
      },
    ],
  },
];

resources.images = [
  {
    id: "images/ambulance.svg",
    displayName: "Ambulance",
    mimeType: "image/svg",
  },
  {
    id: "images/bed.svg",
    displayName: "Bed",
    mimeType: "image/svg",
  },
  {
    id: "images/blooddonation.svg",
    displayName: "Blood Donation",
    mimeType: "image/svg",
  },
  {
    id: "images/siren.svg",
    displayName: "Siren",
    mimeType: "image/svg",
  },
];

resources.reports = [
  {
    id: "reports/CustomersTable.rdlx-json",
    displayName: "Customers Table",
  },
  {
    id: "reports/TaxiDrives.rdlx-json",
    displayName: "Taxi Drive Report",
  },
];
