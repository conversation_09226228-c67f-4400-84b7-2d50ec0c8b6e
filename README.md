# admin-front 精简说明

本分支已按“生成环境简化”要求移除以下功能：

- 暗黑模式：移除切换按钮与相关运行时逻辑，固定为亮色模式。
- 主题自定义：移除颜色主题选择与构建期主题切换插件。

关键变更点：

- `src/settings/projectSetting.ts`：`showDarkModeToggle` 设为 `false`。
- `src/layouts/default/setting/SettingDrawer.tsx`：移除暗黑开关与主题颜色选择 UI。
- `src/layouts/default/setting/handler.ts`：忽略 `CHANGE_THEME` 与 `CHANGE_THEME_COLOR` 事件。
- `src/views/sys/login/Login.vue`、`src/views/system/loginmini/MiniLogin.vue`：移除 `AppDarkModeToggle` 引用。
- `src/components/Application/src/AppDarkModeToggle.vue`：留空实现以保持兼容。
- `src/logics/initAppConfig.ts`：不再初始化暗黑模式，固定亮色逻辑。
- `src/logics/theme/dark.ts`：固定写入 `data-theme='light'`。
- `src/logics/theme/updateBackground.ts`：不再根据暗黑模式分支，统一按亮色处理。
- `build/vite/plugin/index.ts`：停止注册主题相关插件。
- `build/vite/plugin/theme.ts`：去除 `antdDarkThemePlugin`，保留基础变量替换能力（如需）。

注意事项：

- 如需恢复暗黑或主题自定义，请参考以上文件反向启用相应代码。
- 其余 UI 与业务功能不受影响。


